oa-prod$ npm test -- --testPathPattern="shared/src/base/__tests__/modules/cleanup" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=shared/src/base/__tests__/modules/cleanup --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts (233 MB heap size)
  CleanupTemplateManager
    Template Registration
      ✓ should register valid template successfully (8 ms)
      ✓ should validate template structure during registration (18 ms)
      ✕ should detect circular dependencies in template operations (14 ms)
      ✓ should initialize template metrics upon registration (3 ms)
    Template Execution
      ✕ should execute template with target components (7 ms)
      ✕ should handle template execution errors gracefully (3 ms)
      ✓ should generate unique execution IDs (3 ms)
      ✓ should reject execution of non-existent template (2 ms)
    Template Metrics
      ✕ should collect execution metrics during template execution (4 ms)
      ✓ should return empty metrics for non-existent template (3 ms)
      ✓ should return all template metrics when no template ID specified (7 ms)
    Module Integration
      ✓ should integrate with TemplateValidator for validation (2 ms)
      ✓ should integrate with TemplateWorkflowExecutor for execution (2 ms)
      ✓ should integrate with DependencyGraph for dependency management (3 ms)
    Performance & Memory Safety
      ✓ should maintain performance requirements during template operations (2 ms)
      ✓ should properly cleanup resources during shutdown (3 ms)

  ● CleanupTemplateManager › Template Registration › should detect circular dependencies in template operations

    expect(received).rejects.toThrow(expected)

    Expected substring: "contains circular dependencies"
    Received message:   "Template validation failed: Dependency cycles detected: 1 cycle(s) found, Incomplete topological sort - indicates dependency issues"

          185 |         if (!validation.valid) {
          186 |           const errorMessage = `Template validation failed: ${validation.issues.map(i => i.message).join(', ')}`;
        > 187 |           throw new Error(errorMessage);
              |                 ^
          188 |         }
          189 |
          190 |         if (validation.warnings.length > 0) {

          at CleanupTemplateManager.registerTemplate (shared/src/base/modules/cleanup/CleanupTemplateManager.ts:187:17)
          at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:233:7)

      233 |       await expect(templateManager.registerTemplate(cyclicTemplate))
      234 |         .rejects
    > 235 |         .toThrow('contains circular dependencies');
          |          ^
      236 |     });
      237 |
      238 |     it('should initialize template metrics upon registration', async () => {

      at Object.toThrow (node_modules/expect/build/index.js:218:22)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:235:10)

  ● CleanupTemplateManager › Template Execution › should execute template with target components

    expect(received).toBe(expected) // Object.is equality

    Expected: "success"
    Received: "failure"

      273 |       expect(executionTime).toBeLessThan(100); // <100ms requirement
      274 |
    > 275 |       expect(result.status).toBe('success');
          |                             ^
      276 |       expect(result.templateId).toBe('test-template-001');
      277 |       expect(result.executedSteps).toBeGreaterThan(0);
      278 |       expect(result.errors).toHaveLength(0);

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:275:29)

  ● CleanupTemplateManager › Template Execution › should handle template execution errors gracefully

    expect(received).toBe(expected) // Object.is equality

    Expected: "failure"
    Received: "success"

      289 |       );
      290 |
    > 291 |       expect(result.status).toBe('failure');
          |                             ^
      292 |       expect(result.errors.length).toBeGreaterThan(0);
      293 |     });
      294 |

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:291:29)

  ● CleanupTemplateManager › Template Metrics › should collect execution metrics during template execution

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      342 |       const metrics = templateManager.getTemplateMetrics('test-template-001');
      343 |       expect(metrics.executedSteps).toBeGreaterThan(0);
    > 344 |       expect(metrics.totalExecutionTime).toBeGreaterThan(0);
          |                                          ^
      345 |       expect(metrics.averageStepTime).toBeGreaterThan(0);
      346 |     });
      347 |

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:344:42)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts (401 MB heap size)
  TemplateWorkflows
    Basic Workflow Execution
      ✓ should execute simple workflow successfully (3 ms)
      ✓ should handle empty workflow gracefully (6 ms)
      ✓ should update execution metrics during workflow (2 ms)
      ✓ should generate unique execution IDs (2 ms)
    Parallel & Sequential Execution
      ✓ should execute parallel workflow with independent steps (4 ms)
      ✓ should execute sequential workflow with dependencies (3 ms)
      ✓ should respect workflow configuration settings (2 ms)
      ✓ should allow configuration updates (2 ms)
    Retry Logic & Error Handling
      ✕ should retry failed steps according to configuration (3 ms)
      ✕ should handle component registry failures gracefully (4 ms)
      ✕ should execute dry run mode without actual operations (11 ms)
      ✓ should skip conditions when specified in options (2 ms)
    Component Integration & Simulation
      ✓ should integrate with component registry for component discovery (2 ms)
      ✕ should simulate different operation types correctly (2 ms)
      ✓ should handle component overrides in execution options (2 ms)
    Utility Functions & Performance
      ✓ should create workflow executor with factory function (2 ms)
      ✓ should execute workflow using utility function (2 ms)
      ✓ should maintain performance requirements for complex workflows (2 ms)

  ● TemplateWorkflows › Retry Logic & Error Handling › should retry failed steps according to configuration

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      351 |
      352 |       expect(results).toHaveLength(1);
    > 353 |       expect(results[0].retryCount).toBeGreaterThan(0);
          |                                     ^
      354 |     });
      355 |
      356 |     it('should handle component registry failures gracefully', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:353:37)

  ● TemplateWorkflows › Retry Logic & Error Handling › should handle component registry failures gracefully

    expect(received).toBeDefined()

    Received: undefined

      364 |       expect(results).toHaveLength(1);
      365 |       expect(results[0].success).toBe(false);
    > 366 |       expect(results[0].error).toBeDefined();
          |                                ^
      367 |     });
      368 |
      369 |     it('should execute dry run mode without actual operations', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:366:32)

  ● TemplateWorkflows › Retry Logic & Error Handling › should execute dry run mode without actual operations

    expect(received).toEqual(expected) // deep equality

    Expected: ObjectContaining {"dryRun": true}
    Received: [{"componentId": "test-component-1", "executionTime": 0.0409429999999702, "result": {"dryRun": true, "simulatedOperation": "resource-cleanup"}, "retryCount": 0, "rollbackRequired": false, "skipped": false, "stepId": "step-001", "success": true}, {"componentId": "test-component-2", "executionTime": 0.05873199999996359, "result": {"dryRun": true, "simulatedOperation": "resource-cleanup"}, "retryCount": 0, "rollbackRequired": false, "skipped": false, "stepId": "step-001", "success": true}]

      376 |       expect(results).toHaveLength(1);
      377 |       expect(results[0].success).toBe(true);
    > 378 |       expect(results[0].result).toEqual(
          |                                 ^
      379 |         expect.objectContaining({ dryRun: true })
      380 |       );
      381 |     });

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:378:33)

  ● TemplateWorkflows › Component Integration & Simulation › should simulate different operation types correctly

    expect(received).toEqual(expected) // deep equality

    Expected: ObjectContaining {"operationType": "resource-cleanup"}
    Received: [{"componentId": "test-component-1", "executionTime": 0.07449999999971624, "result": {"cleanupTime": 50, "componentId": "test-component-1", "itemsProcessed": 4, "operationType": "resource-cleanup"}, "retryCount": 0, "rollbackRequired": false, "skipped": false, "stepId": "resource-cleanup", "success": true}, {"componentId": "test-component-2", "executionTime": 0.07324100000005274, "result": {"cleanupTime": 50, "componentId": "test-component-2", "itemsProcessed": 1, "operationType": "resource-cleanup"}, "retryCount": 0, "rollbackRequired": false, "skipped": false, "stepId": "resource-cleanup", "success": true}]

      464 |
      465 |       expect(results).toHaveLength(2);
    > 466 |       expect(results[0].result).toEqual(
          |                                 ^
      467 |         expect.objectContaining({ operationType: 'resource-cleanup' })
      468 |       );
      469 |       expect(results[1].result).toEqual(

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:466:33)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts (407 MB heap size)
  TemplateValidation
    Template Structure Validation
      ✓ should validate correct template structure (3 ms)
      ✓ should reject template with missing ID (2 ms)
      ✓ should reject template with no operations (2 ms)
      ✓ should warn about missing description (2 ms)
      ✓ should validate operation structure completeness (2 ms)
    Dependency Validation
      ✓ should validate correct dependency relationships (2 ms)
      ✓ should detect invalid dependency references (2 ms)
      ✓ should detect circular dependencies (3 ms)
    Condition Evaluation
      ✓ should evaluate always condition correctly (4 ms)
      ✓ should evaluate on_success condition correctly (2 ms)
      ✓ should evaluate on_failure condition correctly (2 ms)
      ✓ should evaluate component_exists condition correctly (2 ms)
      ✓ should validate custom condition requirements (2 ms)
    Quality Scoring & Utilities
      ✓ should calculate quality score based on template completeness (2 ms)
      ✕ should penalize quality score for validation issues (2 ms)
      ✓ should match components using regex patterns (2 ms)
      ✓ should handle invalid regex patterns gracefully (2 ms)
      ✓ should use validateTemplate utility function (2 ms)
    Performance & Extended Validation
      ✓ should maintain performance requirements during validation (1 ms)
      ✓ should provide comprehensive extended validation results (2 ms)

  ● TemplateValidation › Quality Scoring & Utilities › should penalize quality score for validation issues

    expect(received).toBeLessThan(expected)

    Expected: < 50
    Received:   80

      485 |       const result = await templateValidator.validateTemplate(lowQualityTemplate);
      486 |
    > 487 |       expect(result.qualityScore).toBeLessThan(50);
          |                                   ^
      488 |     });
      489 |
      490 |     it('should match components using regex patterns', () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts:487:35)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts (417 MB heap size)
  TemplateDependencies
    Basic Graph Operations
      ✓ should add nodes to the graph (2 ms)
      ✓ should add edges between nodes (2 ms)
      ✓ should add dependencies correctly (4 ms)
      ✓ should handle duplicate node additions gracefully (2 ms)
      ✓ should clear the graph completely (1 ms)
    Cycle Detection
      ✓ should detect no cycles in acyclic graph (1 ms)
      ✓ should detect simple two-node cycle (1 ms)
      ✓ should detect complex multi-node cycle (2 ms)
      ✓ should find all cycles in graph (2 ms)
      ✓ should handle self-referencing nodes (1 ms)
    Topological Sorting
      ✕ should perform topological sort on simple DAG (2 ms)
      ✕ should handle complex dependency relationships (1 ms)
      ✓ should return empty array for cyclic graph (1 ms)
      ✓ should handle disconnected components (2 ms)
    Critical Path & Parallel Analysis
      ✓ should calculate critical path correctly (4 ms)
      ✓ should identify parallel execution groups (3 ms)
      ✓ should calculate graph metrics accurately (2 ms)
      ✓ should detect transitive dependencies (2 ms)
    Utility Functions & Performance
      ✓ should create dependency graph from operations (1 ms)
      ✓ should validate dependency graph for issues (1 ms)
      ✓ should detect validation issues in problematic graphs (2 ms)
      ✓ should clone dependency graph accurately (2 ms)
      ✓ should maintain performance requirements for complex graphs (2 ms)
      ✕ should handle edge cases gracefully (5 ms)

  ● TemplateDependencies › Topological Sorting › should perform topological sort on simple DAG

    expect(received).toBeLessThan(expected)

    Expected: < 1
    Received:   2

      205 |
      206 |       expect(sorted).toHaveLength(3);
    > 207 |       expect(sorted.indexOf('C')).toBeLessThan(sorted.indexOf('B'));
          |                                   ^
      208 |       expect(sorted.indexOf('B')).toBeLessThan(sorted.indexOf('A'));
      209 |     });
      210 |

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts:207:35)

  ● TemplateDependencies › Topological Sorting › should handle complex dependency relationships

    expect(received).toBeLessThan(expected)

    Expected: < 1
    Received:   3

      217 |
      218 |       expect(sorted).toHaveLength(4);
    > 219 |       expect(sorted.indexOf('core')).toBeLessThan(sorted.indexOf('lib1'));
          |                                      ^
      220 |       expect(sorted.indexOf('core')).toBeLessThan(sorted.indexOf('lib2'));
      221 |       expect(sorted.indexOf('lib1')).toBeLessThan(sorted.indexOf('main'));
      222 |       expect(sorted.indexOf('lib2')).toBeLessThan(sorted.indexOf('main'));

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts:219:38)

  ● TemplateDependencies › Utility Functions & Performance › should handle edge cases gracefully

    expect(received).toEqual(expected) // deep equality

    - Expected  - 3
    + Received  + 1

    - Array [
    -   "single",
    - ]
    + Array []

      405 |       dependencyGraph.addNode('single');
      406 |       expect(dependencyGraph.topologicalSort()).toEqual(['single']);
    > 407 |       expect(dependencyGraph.getCriticalPath()).toEqual(['single']);
          |                                                 ^
      408 |     });
      409 |   });
      410 | });

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts:407:49)

Test Suites: 4 failed, 4 total
Tests:       12 failed, 66 passed, 78 total
Snapshots:   0 total
Time:        4.322 s
Ran all test suites matching /shared\/src\/base\/__tests__\/modules\/cleanup/i.
