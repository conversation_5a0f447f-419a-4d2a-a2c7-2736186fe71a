npm test -- --testPathPattern="shared/src/base/__tests__" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=shared/src/base/__tests__ --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBufferEnhanced.test.ts (240 MB heap size)
  AtomicCircularBufferEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (7 ms)
      ✓ should preserve base class metrics functionality (3 ms)
      ✓ should handle buffer overflow like base class (2 ms)
    Advanced Buffer Strategies
      ✓ should evict items using LRU policy (2 ms)
      ✓ should evict items using LFU policy (2 ms)
      ✓ should maintain performance with enhanced eviction (11 ms)
      ✓ should call pre-eviction callback when configured (3 ms)
    Buffer Persistence
      ✓ should create and restore from snapshot (4 ms)
      ✓ should validate snapshot integrity (29 ms)
      ✓ should handle snapshot creation performance requirements (3 ms)
    Buffer Analytics
      ✓ should provide comprehensive analytics (4 ms)
      ✓ should calculate hit and miss rates correctly (6 ms)
      ✓ should identify hot and cold items correctly (3 ms)
      ✓ should analyze access patterns (18 ms)
      ✓ should calculate analytics within performance requirements (2 ms)
    Buffer Optimization
      ✓ should generate and apply optimization recommendations (2 ms)
      ✓ should optimize eviction policy when needed (2 ms)
    Performance Validation
      ✓ should meet enhanced operation performance requirements (2 ms)
      ✓ should maintain memory overhead within reasonable limits (3 ms)
      ✓ should handle concurrent access without performance degradation (19 ms)
    Error Handling and Edge Cases
      ✓ should handle zero-size buffer gracefully (2 ms)
      ✓ should handle invalid snapshot gracefully (2 ms)
      ✓ should handle custom eviction function errors (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts (247 MB heap size)
  CleanupTemplateManager
    Template Registration
      ✓ should register valid template successfully (5 ms)
      ✓ should validate template structure during registration (15 ms)
      ✓ should detect circular dependencies in template operations (3 ms)
      ✓ should initialize template metrics upon registration (2 ms)
    Template Execution
      ✓ should execute template with target components (5 ms)
      ✓ should handle template execution errors gracefully (10 ms)
      ✓ should generate unique execution IDs (3 ms)
      ✓ should reject execution of non-existent template (4 ms)
    Template Metrics
      ✓ should collect execution metrics during template execution (3 ms)
      ✓ should return empty metrics for non-existent template (2 ms)
      ✓ should return all template metrics when no template ID specified (2 ms)
    Module Integration
      ✓ should integrate with TemplateValidator for validation (2 ms)
      ✓ should integrate with TemplateWorkflowExecutor for execution (2 ms)
      ✓ should integrate with DependencyGraph for dependency management (7 ms)
    Performance & Memory Safety
      ✓ should maintain performance requirements during template operations (2 ms)
      ✓ should properly cleanup resources during shutdown (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts
  ● Test suite failed to run

    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:16:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'createEnhancedCleanupCoordinator'.

    16   createEnhancedCleanupCoordinator,
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:17:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'getEnhancedCleanupCoordinator'.

    17   getEnhancedCleanupCoordinator,
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:18:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'IDependencyGraph'.

    18   IDependencyGraph,
         ~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:19:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'IDependencyAnalysis'.

    19   IDependencyAnalysis
         ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:147:19 - error TS2339: Property 'registerCleanupOperation' does not exist on type 'CleanupCoordinatorEnhanced'.

    147       coordinator.registerCleanupOperation(name, operation);
                          ~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:370:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    370       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:376:42 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    376       const optimizedOrder = coordinator.optimizeOperationOrder(operations);
                                                 ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:462:56 - error TS2554: Expected 0 arguments, but got 1.

    462       const memoryTemplates = coordinator.getTemplates({ tags: ['memory'] });
                                                               ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:467:58 - error TS2554: Expected 0 arguments, but got 1.

    467       const resourceTemplates = coordinator.getTemplates({
                                                                 ~
    468         operationType: CleanupOperationType.RESOURCE_CLEANUP
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    469       });
        ~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:474:57 - error TS2554: Expected 0 arguments, but got 1.

    474       const author1Templates = coordinator.getTemplates({ author: 'author1' });
                                                                ~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:479:60 - error TS2554: Expected 0 arguments, but got 1.

    479       const memoryNameTemplates = coordinator.getTemplates({ namePattern: 'memory' });
                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:521:41 - error TS2551: Property 'getTemplateMetrics' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'getTemplates'?

    521       const metricsBefore = coordinator.getTemplateMetrics('metrics-template');
                                                ~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:185:10
        185   public getTemplates(): ICleanupTemplate[] {
                     ~~~~~~~~~~~~
        'getTemplates' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:525:40 - error TS2551: Property 'executeTemplateMinimal' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'executeTemplate'?

    525       const result = await coordinator.executeTemplateMinimal(
                                               ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:145:16
        145   public async executeTemplate(
                           ~~~~~~~~~~~~~~~
        'executeTemplate' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:536:40 - error TS2551: Property 'getTemplateMetrics' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'getTemplates'?

    536       const metricsAfter = coordinator.getTemplateMetrics('metrics-template');
                                               ~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:185:10
        185   public getTemplates(): ICleanupTemplate[] {
                     ~~~~~~~~~~~~
        'getTemplates' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:564:33 - error TS2339: Property 'buildDependencyGraph' does not exist on type 'CleanupCoordinatorEnhanced'.

    564       const graph = coordinator.buildDependencyGraph(operations);
                                        ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:602:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    602       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:655:33 - error TS2339: Property 'buildDependencyGraph' does not exist on type 'CleanupCoordinatorEnhanced'.

    655       const graph = coordinator.buildDependencyGraph(operations);
                                        ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:663:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    663       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:706:42 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    706       const optimizedOrder = coordinator.optimizeOperationOrder(operations);
                                                 ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:741:32 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    741       expect(() => coordinator.optimizeOperationOrder(operations)).toThrow(/circular dependencies/i);
                                       ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:788:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    788       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:817:83 - error TS2554: Expected 1-2 arguments, but got 3.

    817       const checkpointId = await coordinator.createCheckpoint(operationId, state, rollbackActions);
                                                                                          ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:845:83 - error TS2554: Expected 1-2 arguments, but got 3.

    845       const checkpointId = await coordinator.createCheckpoint(operationId, state, rollbackActions);
                                                                                          ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:924:58 - error TS2554: Expected 0 arguments, but got 1.

    924       const op1Checkpoints = coordinator.listCheckpoints({ operationId: operationId1 });
                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:929:61 - error TS2554: Expected 0 arguments, but got 1.

    929       const recentCheckpoints = coordinator.listCheckpoints({ since: now });
                                                                    ~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:932:61 - error TS2554: Expected 0 arguments, but got 1.

    932       const futureCheckpoints = coordinator.listCheckpoints({
                                                                    ~
    933         since: new Date(Date.now() + 10000)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    934       });
        ~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:989:80 - error TS2554: Expected 1-2 arguments, but got 3.

    989       const checkpointId = await coordinator.createCheckpoint(operationId, {}, rollbackActions);
                                                                                       ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1081:40 - error TS2551: Property 'executeTemplateMinimal' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'executeTemplate'?

    1081       const result = await coordinator.executeTemplateMinimal(
                                                ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:145:16
        145   public async executeTemplate(
                           ~~~~~~~~~~~~~~~
        'executeTemplate' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1109:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    1109       const analysis = coordinator.analyzeDependencies(operations);
                                            ~~~~~~~~~~~~~~~~~~~

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/TimerCoordinationServiceEnhanced.test.ts (411 MB heap size)
  TimerCoordinationServiceEnhanced
    Timer Pool Management
      ✓ should create timer pools with comprehensive configuration (4 ms)
      ✓ should prevent duplicate pool creation (47 ms)
      ✓ should create pooled timers with strategy selection (3 ms)
      ✓ should handle pool exhaustion strategies (3 ms)
      ✓ should provide comprehensive pool statistics (3 ms)
      ✓ should remove timers from pools properly (3 ms)
    Advanced Scheduling
      ✓ should schedule recurring timers with comprehensive configuration (3 ms)
      ✓ should schedule cron timers with validation (8 ms)
      ✓ should validate cron expressions properly (3 ms)
      ✓ should schedule conditional timers with proper execution (3 ms)
      ✓ should schedule delayed timers with precise timing (2 ms)
      ✓ should schedule priority timers with queue management (2 ms)
      ✓ should validate scheduling preconditions (4 ms)
    Timer Coordination Patterns
      ✓ should create timer groups with comprehensive configuration (4 ms)
      ✓ should prevent duplicate group creation (2 ms)
      ✓ should validate group creation preconditions (8 ms)
      ✓ should synchronize timer groups with comprehensive results (3 ms)
      ✓ should handle synchronization of non-existent groups (2 ms)
      ✓ should create timer chains with workflow support (3 ms)
      ✓ should validate chain step preconditions (4 ms)
      ✓ should create timer barriers with coordination patterns (2 ms)
      ✓ should validate barrier creation preconditions (3 ms)
      ✓ should pause and resume timer groups (2 ms)
      ✓ should destroy timer groups with comprehensive cleanup (2 ms)
    Performance Requirements
      ✓ should meet pool operation performance requirements (<5ms) (5 ms)
      ✓ should meet scheduling performance requirements (<10ms) (2 ms)
      ✓ should meet synchronization performance requirements (<20ms) (2 ms)
    Phase Integration
      ✓ should initialize without phase integrations when disabled (2 ms)
      ✓ should handle phase integration gracefully when enabled (2 ms)
    Enterprise Error Handling
      ✓ should provide enhanced error context for all operations (2 ms)
      ✓ should classify errors appropriately (2 ms)
    Singleton and Lifecycle
      ✓ should maintain singleton pattern (1 ms)
      ✓ should support instance reset for testing (1 ms)
      ✓ should handle emergency cleanup (1 ms)
      ✓ should initialize and shutdown gracefully (2 ms)
    Comprehensive Integration
      ✓ should demonstrate complete Phase 3 functionality (5 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.test.ts (439 MB heap size)
  AtomicCircularBuffer
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (3 ms)
      ✓ should maintain size consistency under rapid additions (2 ms)
      ✓ should handle automatic cleanup correctly (2 ms)
    Async Operations
      ✓ should handle addItem async operations correctly (2 ms)
      ✓ should handle removeItem async operations correctly (2 ms)
      ✓ should handle clear operations correctly (5 ms)
      ✓ should handle shutdown operations correctly (1 ms)
    Enhanced Concurrent Access and Race Conditions
      Basic concurrent operations
        ✓ should handle concurrent additions without corruption (29 ms)
        ✓ should handle mixed concurrent operations (10 ms)
        ✓ should handle concurrent access during shutdown (2 ms)
      Advanced race condition scenarios
        ✓ should handle rapid key updates without data loss (7 ms)
        ✓ should handle concurrent add/remove on same keys (20 ms)
        ✓ should handle concurrent operations with buffer overflow (17 ms)
        ✓ should handle concurrent clear operations (4 ms)
      Stress testing and performance under concurrency
        ✓ should handle high-concurrency stress test (4 ms)
        ✓ should maintain performance under sustained load (test environment) (2 ms)
        ✓ should handle concurrent operations with different data types (7 ms)
      Edge cases in concurrent scenarios
        ✓ should handle concurrent operations on empty buffer (6 ms)
        ✓ should handle concurrent operations during buffer state transitions (4 ms)
    Memory Pressure Handling
      ✓ should handle memory pressure gracefully (12 ms)
      ✓ should maintain performance under load (2 ms)
    Comprehensive Metrics Testing
      Basic metrics tracking
        ✓ should initialize metrics with zero values (5 ms)
        ✓ should track add operations correctly (2 ms)
        ✓ should track remove operations correctly (2 ms)
        ✓ should track clear operations correctly (2 ms)
        ✓ should track automatic cleanup operations (2 ms)
      Metrics defensive copying
        ✓ should return defensive copy of metrics (2 ms)
        ✓ should handle metrics during concurrent access (7 ms)
      Metrics edge cases and overflow scenarios
        ✓ should handle large operation counts (5 ms)
        ✓ should maintain metrics accuracy during buffer overflow (5 ms)
        ✓ should handle metrics during rapid operations (26 ms)
        ✓ should reset metrics appropriately on clear (2 ms)
      Sync validation metrics
        ✓ should track sync validations (1 ms)
        ✓ should initialize sync error tracking correctly (2 ms)
    Resource Limits and Integration
      ✓ should integrate properly with MemorySafeResourceManager (2 ms)
      ✓ should handle resource exhaustion gracefully (1 ms)
    Basic Functionality - Enhanced Coverage
      Constructor and Initialization
        ✓ should create buffer with valid max size (1 ms)
        ✓ should handle edge case max sizes (2 ms)
        ✓ should handle zero max size gracefully (4 ms)
      getItem method edge cases
        ✓ should handle non-existent keys (1 ms)
        ✓ should handle special character keys (2 ms)
        ✓ should handle unicode keys (2 ms)
      getAllItems method edge cases
        ✓ should return empty map when buffer is empty (1 ms)
        ✓ should return defensive copy of items (2 ms)
        ✓ should handle buffer at max capacity (2 ms)
      getSize method edge cases
        ✓ should return 0 for empty buffer (2 ms)
        ✓ should track size accurately during operations (1 ms)
        ✓ should maintain size consistency during overflow (2 ms)
    Error Conditions and Edge Cases
      ✓ should handle operations on uninitialized buffer (3 ms)
      ✓ should handle double initialization gracefully (2 ms)
      ✓ should handle double shutdown gracefully (2 ms)
      ✓ should handle operations after shutdown (1 ms)
      ✓ should handle invalid keys and values (2 ms)
      ✓ should handle extremely long keys (2 ms)
      ✓ should handle duplicate key additions (1 ms)
      ✓ should handle removing non-existent items (1 ms)
    Comprehensive Error Handling and Recovery
      Invalid input handling
        ✓ should handle null and undefined keys gracefully (2 ms)
        ✓ should handle extremely large keys (1 ms)
        ✓ should handle keys with problematic characters (2 ms)
        ✓ should handle circular reference values (1 ms)
        ✓ should handle extremely large values (9 ms)
      Buffer state error conditions
        ✓ should handle operations on corrupted buffer state (2 ms)
        ✓ should handle rapid state changes (2 ms)
        ✓ should handle buffer overflow edge cases (6 ms)
      Resource exhaustion scenarios
        ✓ should handle memory pressure gracefully (5 ms)
        ✓ should handle timer/interval resource limits (3 ms)
      Recovery and resilience
        ✓ should recover from temporary errors (2 ms)
        ✓ should handle graceful degradation (2 ms)
        ✓ should maintain data integrity during errors (6 ms)
    Logging Interface (ILoggingService)
      logInfo method
        ✓ should log info messages correctly (2 ms)
        ✓ should log info messages with details (2 ms)
        ✓ should handle empty info messages (2 ms)
        ✓ should handle complex details objects (2 ms)
      logWarning method
        ✓ should log warning messages correctly (1 ms)
        ✓ should log warning messages with details (1 ms)
        ✓ should handle special characters in warning messages (2 ms)
      logError method
        ✓ should log error messages with Error objects (2 ms)
        ✓ should log error messages with string errors (2 ms)
        ✓ should log error messages with details (2 ms)
        ✓ should handle null/undefined errors (2 ms)
        ✓ should handle complex error objects (4 ms)
      logDebug method
        ✓ should log debug messages in development environment (2 ms)
        ✓ should log debug messages when DEBUG flag is set (1 ms)
        ✓ should not log debug messages in production without DEBUG flag (1 ms)
        ✓ should log debug messages with details (2 ms)
      Logging integration with buffer operations
        ✓ should not interfere with normal buffer operations (2 ms)
        ✓ should handle logging during concurrent operations (5 ms)
    Performance and Memory Benchmarks
      Performance benchmarks
        ✓ should maintain fast add operations under load (2 ms)
        ✓ should maintain fast remove operations under load (5 ms)
        ✓ should maintain fast read operations under load (5 ms)
        ✓ should handle mixed operation performance (4 ms)
        ✓ should scale performance with buffer size (3 ms)
      Memory usage and leak detection
        ✓ should not leak memory during normal operations (2 ms)
        ✓ should maintain stable memory usage under sustained load (5 ms)
        ✓ should clean up memory on buffer clear (2 ms)
        ✓ should clean up all resources on shutdown (2 ms)
      Resource cleanup verification
        ✓ should properly clean up intervals and timeouts (1 ms)
        ✓ should handle multiple buffer instances without resource conflicts (9 ms)
        ✓ should handle rapid create/destroy cycles (3 ms)
    Synchronization and Validation
      Internal state consistency
        ✓ should maintain consistency between items map and insertion order (2 ms)
        ✓ should handle rapid add/remove cycles without corruption (6 ms)
        ✓ should maintain order integrity during overflow (2 ms)
      Concurrent access synchronization
        ✓ should handle sequential add operations without state corruption (2 ms)
        ✓ should handle mixed sequential operations safely (1 ms)
        ✓ should handle concurrent operations during buffer overflow (6 ms)
      Lock mechanism validation
        ✓ should execute operations sequentially with operation lock (2 ms)
        ✓ should handle sequential operations efficiently (2 ms)
      Validation and error detection
        ✓ should track sync validations in metrics (1 ms)
        ✓ should handle validation during high-frequency operations (25 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeResourceManagerEnhanced.test.ts (458 MB heap size)
  MemorySafeResourceManagerEnhanced
    Enhanced Initialization and Lifecycle
      ✓ should initialize with enhanced features enabled (3 ms)
      ✓ should maintain backward compatibility with base class (3 ms)
      ✓ should shutdown enhanced features properly (5 ms)
    Resource Pool Management
      ✓ should create resource pools efficiently (2 ms)
      ✓ should borrow and return resources efficiently (3 ms)
      ✓ should handle pool exhaustion gracefully (22 ms)
      ✓ should validate resources before returning to pool (2 ms)
    Dynamic Resource Scaling
      ✓ should enable dynamic scaling with proper configuration (2 ms)
      ✓ should perform scaling analysis within performance bounds (3 ms)
      ✓ should respect cooldown periods (150 ms)
    Enhanced Reference Counting
      ✓ should create advanced shared resources with enhanced tracking (3 ms)
      ✓ should handle strong reference counting correctly (3 ms)
      ✓ should handle weak references without preventing cleanup (2 ms)
      ✓ should track access patterns and metadata (1 ms)
      ✓ should perform reference operations within performance bounds (1 ms)
    Resource Lifecycle Events
      ✓ should enable lifecycle events with proper configuration (2 ms)
      ✓ should emit events for resource operations (2 ms)
      ✓ should buffer and flush events efficiently (2 ms)
      ✓ should emit events within performance bounds (2 ms)
      ✓ should handle event handler errors gracefully (7 ms)
    Performance Validation
      ✓ should maintain 0% overhead in test mode (2 ms)
      ✓ should meet individual operation performance requirements (2 ms)
      ✓ should handle high-volume operations efficiently (2 ms)
    Backward Compatibility
      ✓ should preserve all base class functionality (2 ms)
      ✓ should maintain base class event emission (2 ms)
      ✓ should preserve base class error handling (7 ms)
      ✓ should maintain base class resource limits (2 ms)
    Integration and Health
      ✓ should integrate enhanced features seamlessly (2 ms)
      ✓ should handle resource optimization cycles (2 ms)
      ✓ should maintain health under stress conditions (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistryEnhanced.test.ts (461 MB heap size)
  EventHandlerRegistryEnhanced
    Backward Compatibility
      ✓ should maintain all base class functionality (3 ms)
      ✓ should preserve base class metrics functionality (2 ms)
      ✓ should handle handler unregistration like base class (1 ms)
    Event Emission System
      ✓ should emit events to all registered handlers (3 ms)
      ✓ should handle handler errors gracefully (5 ms)
      ✓ should emit events to specific clients (2 ms)
      ✓ should process event batches correctly (2 ms)
      ✓ should handle event emission timeout (13 ms)
      ✓ should meet performance requirements for emission (3 ms)
    Handler Middleware System
      ✓ should execute middleware in priority order (2 ms)
      ✓ should skip handler when middleware returns false (2 ms)
      ✓ should handle errors through middleware (2 ms)
      ✓ should execute after-handler middleware (5 ms)
      ✓ should remove middleware correctly (2 ms)
      ✓ should meet middleware performance requirements (2 ms)
    Advanced Handler Deduplication
      ✓ should detect duplicate handlers by reference (2 ms)
      ✓ should detect duplicate handlers by signature (2 ms)
      ✓ should use custom deduplication function (2 ms)
      ✓ should merge metadata on duplicate detection (2 ms)
      ✓ should meet deduplication performance requirements (2 ms)
    Event Buffering and Queuing
      ✓ should buffer events and flush periodically (4 ms)
      ✓ should auto-flush when threshold is reached (6 ms)
      ✓ should handle buffer overflow correctly (7 ms)
      ✓ should process events with priority strategy (3 ms)
      ✓ should meet buffer operation performance requirements (6 ms)
    Enhanced Metrics and Monitoring
      ✓ should provide enhanced metrics (2 ms)
      ✓ should track middleware execution metrics (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.performance.test.ts (456 MB heap size)
  AtomicCircularBuffer - Performance
    Performance Benchmarks
      ✓ should maintain fast add operations under load (3 ms)
      ✓ should maintain fast get operations under load (49 ms)
      ✓ should maintain fast remove operations under load (4 ms)
      ✓ should handle mixed operations efficiently (2 ms)
    Concurrent Access Performance
      ✓ should handle basic concurrent operations (7 ms)
      ✓ should handle concurrent additions without corruption (8 ms)
      ✓ should handle rapid key updates without data loss (5 ms)
    Stress Testing
      ✓ should handle high-concurrency stress test (4 ms)
      ✓ should maintain performance with large datasets (53 ms)
    Performance Metrics
      ✓ should track operation metrics accurately (2 ms)
      ✓ should provide performance insights through metrics (6 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafetyManagerEnhanced.test.ts (474 MB heap size)
  MemorySafetyManagerEnhanced
    Component Discovery
      ✓ should discover and register memory-safe components (8 ms)
      ✓ should validate component compatibility with comprehensive checks (6 ms)
      ✓ should auto-integrate compatible components (6 ms)
      ✓ should handle component discovery performance requirements (7 ms)
    System Coordination
      ✓ should create and coordinate component groups (6 ms)
      ✓ should execute component chains sequentially (4 ms)
      ✓ should create resource sharing groups (4 ms)
      ✓ should handle group operations performance requirements (4 ms)
    System Shutdown
      ✓ should orchestrate graceful system shutdown (4 ms)
      ✓ should orchestrate priority system shutdown (4 ms)
      ✓ should orchestrate emergency system shutdown (9 ms)
    Integration with Enhanced Components
      ✓ should integrate with all previous phase components (8 ms)
      ✓ should maintain backward compatibility with base MemorySafetyManager (6 ms)
      ✓ should handle enhanced metrics collection (4 ms)
    Performance and Compliance
      ✓ should meet all performance requirements (4 ms)
      ✓ should maintain Anti-Simplification Policy compliance (4 ms)
      ✓ should handle Jest fake timers compatibility (4 ms)
      ✓ should maintain memory usage within limits (4 ms)
    Factory Functions
      ✓ should create enhanced memory safety manager instances (4 ms)
      ✓ should handle singleton pattern correctly (4 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeResourceManager.test.ts (472 MB heap size)
  MemorySafeResourceManager
    Initialization and Lifecycle
      ✓ should initialize correctly with default limits (2 ms)
      ✓ should initialize correctly with custom limits (1 ms)
      ✓ should handle double initialization gracefully (1 ms)
      ✓ should shutdown correctly (2 ms)
      ✓ should handle double shutdown gracefully (2 ms)
      ✓ should prevent operations during shutdown (1 ms)
    Resource Creation and Management
      Safe Interval Creation
        ✓ should create safe intervals correctly (5 ms)
        ✓ should enforce interval limits (3 ms)
        ✓ should handle interval errors gracefully (3 ms)
      Safe Timeout Creation
        ✓ should create safe timeouts correctly (3 ms)
        ✓ should enforce timeout limits (2 ms)
        ✓ should auto-cleanup timeouts after execution (2 ms)
      Shared Resource Management
        ✓ should create and manage shared resources (2 ms)
        ✓ should handle reference counting correctly (2 ms)
    Resource Limits and Enforcement
      ✓ should track resource metrics correctly (2 ms)
      ✓ should report health status correctly (5 ms)
      ✓ should handle memory pressure detection (2 ms)
    Cleanup and Resource Management
      ✓ should perform periodic cleanup (2 ms)
      ✓ should force cleanup all resources (2 ms)
      ✓ should handle emergency cleanup correctly (1 ms)
    Error Handling and Edge Cases
      ✓ should handle resource creation errors gracefully (2 ms)
      ✓ should handle cleanup errors gracefully (22 ms)
      ✓ should handle operations on uninitialized manager (2 ms)
      ✓ should handle concurrent operations safely (2 ms)
    Memory Leak Detection
      ✓ should not leak memory during normal operations (2 ms)
      ✓ should clean up global instances on process events (2 ms)
    Singleton Factory
      ✓ should create singleton instances correctly (1 ms)
      ✓ should recreate singleton after shutdown (4 ms)
    Integration with Event System
      ✓ should emit lifecycle events correctly (2 ms)
      ✓ should emit error events for resource errors (1 ms)
    Performance Benchmarks
      ✓ should create resources within performance bounds (1 ms)
      ✓ should handle resource cleanup within performance bounds (1 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.memory-leak.test.ts (485 MB heap size)
  EventHandlerRegistry Memory Leak Validation
    Rapid Subscription/Unsubscription Cycles
      ✓ should not leak memory during rapid handler registration/unregistration (20 ms)
      ✓ should handle partial cleanup scenarios without leaks (8 ms)
    Client Disconnection Scenarios
      ✓ should completely clean up all client handlers on disconnection (20 ms)
      ✓ should handle concurrent client disconnections without leaks (10 ms)
    Handler Timeout and Orphan Cleanup
      ✓ should detect and clean up orphaned handlers (4 ms)
    High-Volume Concurrent Operations
      ✓ should handle high-volume operations without memory leaks (19 ms)
    Circular Reference Prevention
      ✓ should not retain circular references after cleanup (2 ms)
    toString() Pattern Elimination Validation
      ✓ should use deterministic handler IDs instead of toString() patterns (2 ms)
      ✓ should handle function references without relying on string representation (2 ms)
    Memory Metrics Accuracy Validation
      ✓ should accurately track memory state in metrics (10 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.memory.test.ts (489 MB heap size)
  AtomicCircularBuffer - Memory Safety
    Memory Leak Detection
      ✓ should not leak memory during normal operations (3 ms)
      ✓ should properly clean up large datasets (61 ms)
      ✓ should handle memory pressure gracefully (3 ms)
    Resource Cleanup Verification
      ✓ should properly clean up intervals and timeouts (2 ms)
      ✓ should handle shutdown during active operations (6 ms)
    Memory Boundary Enforcement
      ✓ should enforce maximum size limits (2 ms)
      ✓ should handle zero-size buffer gracefully (2 ms)
    Memory Monitoring Integration
      ✓ should provide accurate resource metrics (1 ms)
      ✓ should track memory usage patterns (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/TimerCoordinationService.test.ts (497 MB heap size)
  TimerCoordinationService
    Singleton Pattern
      ✓ should return same instance when called multiple times (2 ms)
      ✓ should work with getTimerCoordinator helper (2 ms)
      ✓ should maintain configuration across getInstance calls (1 ms)
    Timer Creation and Management
      Coordinated Interval Creation
        ✓ should create coordinated intervals successfully (2 ms)
        ✓ should prevent duplicate timer creation (2 ms)
        ✓ should allow forced timer recreation (1 ms)
        ✓ should adjust intervals for test environments (2 ms)
      Timer Limits and Enforcement
        ✓ should enforce per-service timer limits (18 ms)
        ✓ should enforce global timer limits (6 ms)
        ✓ should track timers per service correctly (2 ms)
      Timer Removal
        ✓ should remove coordinated timers correctly (1 ms)
        ✓ should handle removal of non-existent timers gracefully (2 ms)
        ✓ should update service counts when removing timers (2 ms)
    Timer Statistics and Monitoring
      ✓ should provide accurate timer statistics (2 ms)
      ✓ should track timer execution counts (2 ms)
      ✓ should identify oldest and most active timers (2 ms)
    Error Handling and Edge Cases
      ✓ should handle callback errors gracefully (1 ms)
      ✓ should handle invalid timer parameters (2 ms)
      ✓ should handle empty or invalid service IDs (1 ms)
      ✓ should handle concurrent timer operations (2 ms)
    Integration with MemorySafeResourceManager
      ✓ should integrate properly with base class functionality (4 ms)
      ✓ should handle initialization lifecycle correctly (1 ms)
      ✓ should handle shutdown lifecycle correctly (2 ms)
    Memory Leak Prevention
      ✓ should not leak memory during normal operations (1 ms)
      ✓ should clean up timer metadata on removal (1 ms)
      ✓ should handle rapid timer creation and removal (2 ms)
    Environment-Specific Behavior
      ✓ should handle test environment configurations (1 ms)
      ✓ should apply different limits in different environments (2 ms)
    Performance Benchmarks
      ✓ should create timers within performance bounds (2 ms)
      ✓ should handle timer removal within performance bounds (1 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts (494 MB heap size)
  TemplateDependencies
    Basic Graph Operations
      ✓ should add nodes to the graph (3 ms)
      ✓ should add edges between nodes (1 ms)
      ✓ should add dependencies correctly (2 ms)
      ✓ should handle duplicate node additions gracefully (1 ms)
      ✓ should clear the graph completely (1 ms)
    Cycle Detection
      ✓ should detect no cycles in acyclic graph (1 ms)
      ✓ should detect simple two-node cycle (1 ms)
      ✓ should detect complex multi-node cycle (2 ms)
      ✓ should find all cycles in graph (1 ms)
      ✓ should handle self-referencing nodes (5 ms)
    Topological Sorting
      ✓ should perform topological sort on simple DAG (1 ms)
      ✓ should handle complex dependency relationships (2 ms)
      ✓ should return empty array for cyclic graph (2 ms)
      ✓ should handle disconnected components (2 ms)
    Critical Path & Parallel Analysis
      ✓ should calculate critical path correctly (1 ms)
      ✓ should identify parallel execution groups (1 ms)
      ✓ should calculate graph metrics accurately (2 ms)
      ✓ should detect transitive dependencies (1 ms)
    Utility Functions & Performance
      ✓ should create dependency graph from operations (1 ms)
      ✓ should validate dependency graph for issues (2 ms)
      ✓ should detect validation issues in problematic graphs (1 ms)
      ✓ should clone dependency graph accurately (4 ms)
      ✓ should maintain performance requirements for complex graphs (2 ms)
      ✓ should handle edge cases gracefully (3 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/CleanupCoordinator.test.ts (507 MB heap size)
  CleanupCoordinator
    Basic Operation Management
      ✓ should schedule and execute cleanup operations (3 ms)
      ✓ should handle operation priorities correctly (2 ms)
      ✓ should cancel queued operations (2 ms)
    Concurrent Operation Testing
      ✓ should handle multiple concurrent operations (12 ms)
      ✓ should handle operation failures and retries (2 ms)
      ✓ should handle permanent failures after max retries (2 ms)
    Metrics and Monitoring
      ✓ should track operation metrics accurately (2 ms)
      ✓ should track operation types and priorities (1 ms)
      ✓ should track operation metrics accurately (2 ms)
      ✓ should track operation types and priorities (2 ms)
    Basic Functionality
      ✓ should provide basic cleanup coordination (2 ms)
    Conflict Prevention Validation
      ✓ should detect and prevent conflicting operations (3 ms)
      ✓ should handle component-level locking (2 ms)
    Dependency Management
      ✓ should handle operation dependencies correctly (6 ms)
    Force Cleanup
      ✓ should execute force cleanup immediately (2 ms)
      ✓ should handle force cleanup failures (19 ms)
    Memory Leak Prevention
      ✓ should prevent memory leaks during cleanup cycles (5 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/LoggingMixin.test.ts (515 MB heap size)
  LoggingMixin
    SimpleLogger
      Basic Logging Functionality
        ✓ should log info messages correctly (2 ms)
        ✓ should log warning messages correctly (2 ms)
        ✓ should log error messages correctly with Error objects (2 ms)
        ✓ should log error messages correctly with string errors (1 ms)
        ✓ should handle undefined details gracefully (1 ms)
      Debug Logging Behavior
        ✓ should log debug messages in development environment (5 ms)
        ✓ should log debug messages when DEBUG env var is set (2 ms)
        ✓ should not log debug messages in production without DEBUG flag (2 ms)
      Memory-Safe Logging
        ✓ should handle large details objects without memory issues (2 ms)
        ✓ should handle circular reference objects safely (1 ms)
        ✓ should handle null and undefined errors correctly (1 ms)
      Performance Impact
        ✓ should complete high-volume logging within reasonable time (3 ms)
        ✓ should handle concurrent logging without corruption (2 ms)
    withLogging Mixin
      Mixin Integration
        ✓ should add logging functionality to base class (2 ms)
        ✓ should implement ILoggingService interface correctly (2 ms)
        ✓ should preserve base class inheritance chain (5 ms)
      Mixin Logging Behavior
        ✓ should log with correct service name prefix (2 ms)
        ✓ should handle all log levels through mixin (2 ms)
      Multiple Mixin Instances
        ✓ should handle multiple service instances with different names (2 ms)
        ✓ should maintain separate logging contexts (2 ms)
    Integration with BaseTrackingService
      ✓ should integrate seamlessly with service inheritance patterns (2 ms)
      ✓ should handle service lifecycle with logging (1 ms)
    Memory Leak Prevention
      ✓ should not retain references after logging (1 ms)
      ✓ should handle rapid creation and destruction of loggers (1 ms)
    Error Handling and Edge Cases
      ✓ should handle empty service names gracefully (1 ms)
      ✓ should handle special characters in service names (2 ms)
      ✓ should handle extremely long messages (1 ms)
      ✓ should handle console method failures gracefully (14 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeSystem.integration.test.ts (517 MB heap size)
  Memory Safe System Integration Tests
    Cross-Component Integration
      ✓ should initialize all components successfully (5 ms)
      ✓ should coordinate operations across all components (5 ms)
      ✓ should handle cross-component dependencies (5 ms)
    System-Wide Memory Leak Prevention
      ✓ should prevent memory leaks during normal operations (5 ms)
      ✓ should detect and handle memory leaks (7 ms)
    Coordinated Shutdown Procedures
      ✓ should execute coordinated shutdown across all components (4 ms)
      ✓ should handle shutdown timeout gracefully (5 ms)
    Error Conditions and Recovery
      ✓ should handle component failures gracefully (5 ms)
      ✓ should recover from emergency cleanup scenarios (4 ms)
    Performance Impact Validation
      ✓ should maintain low performance overhead (5 ms)
      ✓ should scale efficiently with increased load (5 ms)
    Real-World Usage Patterns
      ✓ should handle web server simulation (4 ms)
      ✓ should handle high-frequency operations (4 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeResourceManagerEnhanced.performance.test.ts (517 MB heap size)
  MemorySafeResourceManagerEnhanced Performance Validation
    Resource Operation Performance (<5ms requirement)
      ✓ should create resource pools in <5ms (4 ms)
      ✓ should create advanced shared resources in <5ms (3 ms)
      ✓ should perform reference operations in <1ms (8 ms)
      ✓ should handle resource pool creation efficiently (3 ms)
    Memory Efficiency Validation
      ✓ should demonstrate memory efficiency in enhanced implementation (5 ms)
      ✓ should prevent memory leaks in resource pools (7 ms)
      ✓ should efficiently manage reference counting memory (4 ms)
    Zero Overhead in Test Mode Validation
      ✓ should have 0% overhead with Jest fake timers (2 ms)
      ✓ should handle timer operations efficiently in test mode (3 ms)
    Production Overhead Validation (<5% requirement)
      ✓ should maintain efficient performance in enhanced implementation (3 ms)
      ✓ should handle high-volume operations efficiently (15 ms)
    Concurrent Operation Performance
      ✓ should handle concurrent resource pool operations efficiently (4 ms)
      ✓ should maintain performance under scaling operations (3 ms)
    Event Emission Performance Impact
      ✓ should have minimal performance impact from lifecycle events (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.test.ts (513 MB heap size)
  EventHandlerRegistry
    Basic Functionality
      ✓ should create registry with valid configuration (2 ms)
      ✓ should register handlers successfully (2 ms)
      ✓ should unregister handlers successfully (1 ms)
      ✓ should return false when unregistering non-existent handler (2 ms)
    Handler Retrieval
      ✓ should retrieve handlers for specific event type (1 ms)
      ✓ should return empty array for non-existent event type (1 ms)
      ✓ should update lastUsed timestamp when retrieving handlers (3 ms)
    Client Management
      ✓ should unregister all handlers for a client (2 ms)
      ✓ should return 0 when unregistering handlers for non-existent client (2 ms)
      ✓ should enforce client handler limits (15 ms)
    Metrics and Monitoring
      ✓ should track handler metrics correctly (4 ms)
      ✓ should initialize metrics with zero values (1 ms)
    Error Handling
      ✓ should throw error for invalid registration parameters (2 ms)
      ✓ should handle registration with metadata (2 ms)
    Memory Safety
      ✓ should perform emergency cleanup when global limit exceeded (2 ms)
      ✓ should not leak memory during normal operations (2 ms)
    Logging Interface
      ✓ should log info messages correctly (2 ms)
      ✓ should log warning messages correctly (1 ms)
      ✓ should log error messages correctly (1 ms)
      ✓ should log debug messages correctly (2 ms)
    Global Instance Management
      ✓ should provide global instance access (1 ms)
      ✓ should return same instance on multiple calls (1 ms)
      ✓ should reset global instance correctly (4 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts (522 MB heap size)
  TemplateWorkflows
    Basic Workflow Execution
      ✓ should execute simple workflow successfully (2 ms)
      ✓ should handle empty workflow gracefully (2 ms)
      ✕ should update execution metrics during workflow (3 ms)
      ✓ should generate unique execution IDs (2 ms)
    Parallel & Sequential Execution
      ✓ should execute parallel workflow with independent steps (2 ms)
      ✓ should execute sequential workflow with dependencies (5 ms)
      ✓ should respect workflow configuration settings (3 ms)
      ✓ should allow configuration updates (3 ms)
    Retry Logic & Error Handling
      ✓ should retry failed steps according to configuration (3 ms)
      ✓ should handle component registry failures gracefully (2 ms)
      ✓ should execute dry run mode without actual operations (2 ms)
      ✓ should skip conditions when specified in options (2 ms)
    Component Integration & Simulation
      ✓ should integrate with component registry for component discovery (2 ms)
      ✓ should simulate different operation types correctly (2 ms)
      ✓ should handle component overrides in execution options (2 ms)
    Utility Functions & Performance
      ✓ should create workflow executor with factory function (2 ms)
      ✓ should execute workflow using utility function (6 ms)
      ✓ should maintain performance requirements for complex workflows (2 ms)

  ● TemplateWorkflows › Basic Workflow Execution › should update execution metrics during workflow

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      205 |
      206 |       expect(execution.metrics.executedSteps).toBeGreaterThan(0);
    > 207 |       expect(execution.metrics.totalExecutionTime).toBeGreaterThan(0);
          |                                                    ^
      208 |     });
      209 |
      210 |     it('should generate unique execution IDs', () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:207:52)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts (528 MB heap size)
  TemplateValidation
    Template Structure Validation
      ✓ should validate correct template structure (3 ms)
      ✓ should reject template with missing ID (1 ms)
      ✓ should reject template with no operations (4 ms)
      ✓ should warn about missing description (2 ms)
      ✓ should validate operation structure completeness (2 ms)
    Dependency Validation
      ✓ should validate correct dependency relationships (1 ms)
      ✓ should detect invalid dependency references (1 ms)
      ✓ should detect circular dependencies (2 ms)
    Condition Evaluation
      ✓ should evaluate always condition correctly (1 ms)
      ✓ should evaluate on_success condition correctly (1 ms)
      ✓ should evaluate on_failure condition correctly (1 ms)
      ✓ should evaluate component_exists condition correctly (1 ms)
      ✓ should validate custom condition requirements (2 ms)
    Quality Scoring & Utilities
      ✓ should calculate quality score based on template completeness (1 ms)
      ✓ should penalize quality score for validation issues (5 ms)
      ✓ should match components using regex patterns (1 ms)
      ✓ should handle invalid regex patterns gracefully (1 ms)
      ✓ should use validateTemplate utility function (2 ms)
    Performance & Extended Validation
      ✓ should maintain performance requirements during validation (1 ms)
      ✓ should provide comprehensive extended validation results (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/EventHandlerRegistry.integration.test.ts (540 MB heap size)
  EventHandlerRegistry Integration Tests
    M0 Testing Compatibility
      ✓ should handle extensive event subscription/unsubscription cycles without memory leaks (3 ms)
      ✓ should provide deterministic cleanup on client disconnection (6 ms)
      ✓ should detect and report orphaned handlers (2 ms)
    Performance and Scalability
      ✓ should handle high-volume handler registration efficiently (7 ms)
      ✓ should maintain consistent performance under concurrent access (7 ms)
    Error Handling and Recovery
      ✓ should handle invalid handler operations gracefully (6 ms)
      ✓ should enforce handler limits correctly (22 ms)
    Memory Safety Validation
      ✓ should not retain references after handler cleanup (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.core.test.ts (541 MB heap size)
  AtomicCircularBuffer - Core Functionality
    Constructor and Initialization
      ✓ should create buffer with valid max size (2 ms)
      ✓ should initialize with empty state (1 ms)
      ✓ should handle zero max size (2 ms)
      ✓ should handle large max size (1 ms)
    Basic Add Operations
      ✓ should add single item correctly (2 ms)
      ✓ should add multiple items correctly (1 ms)
      ✓ should handle duplicate keys by updating values (4 ms)
      ✓ should handle empty string keys (2 ms)
      ✓ should handle special character keys (1 ms)
    Basic Remove Operations
      ✓ should remove existing item correctly (1 ms)
      ✓ should return false for non-existent key (2 ms)
      ✓ should handle removing all items (2 ms)
    Basic Get Operations
      ✓ should retrieve existing items correctly (2 ms)
      ✓ should return undefined for non-existent keys (1 ms)
      ✓ should handle getAllItems correctly (2 ms)
      ✓ should return defensive copy of all items (2 ms)
    Size Management
      ✓ should track size accurately (1 ms)
      ✓ should enforce maximum size limits (5 ms)
      ✓ should handle size correctly with duplicate keys (1 ms)
    Circular Buffer Behavior
      ✓ should implement circular behavior when full (2 ms)
      ✓ should maintain insertion order in circular behavior (1 ms)
    Resource Management Integration
      ✓ should integrate with MemorySafeResourceManager (1 ms)
      ✓ should handle initialization and shutdown properly (2 ms)
    Basic Metrics
      ✓ should initialize metrics with zero values (1 ms)
      ✓ should track basic operations in metrics (2 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/MemorySafeResourceManagerEnhanced.integration.test.ts (543 MB heap size)
  MemorySafeResourceManagerEnhanced Integration Tests
    System Initialization and Lifecycle Integration
      ✓ should initialize enhanced manager within full system context (3 ms)
      ✓ should handle system shutdown with proper cleanup coordination (2 ms)
      ✓ should maintain system stability under concurrent operations (4 ms)
    Enhanced Lifecycle Events Integration
      ✓ should integrate enhanced lifecycle events with system event capture (2 ms)
      ✓ should handle event handler errors gracefully in integrated environment (2 ms)
      ✓ should coordinate event batching with system event capture (1 ms)
    AtomicCircularBuffer Integration
      ✓ should integrate enhanced event buffering with AtomicCircularBuffer (2 ms)
      ✓ should handle buffer overflow gracefully (2 ms)
    Performance Integration Testing
      ✓ should maintain <5% production overhead in integration context (9 ms)
      ✓ should handle high-volume concurrent operations efficiently (4 ms)
    System-Wide Functionality Validation
      ✓ should maintain backward compatibility with existing functionality (2 ms)
      ✓ should provide comprehensive enhanced functionality without conflicts (5 ms)

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 PASS  shared/src/base/__tests__/AtomicCircularBuffer.integration.test.ts (545 MB heap size)
  AtomicCircularBuffer - Integration
    MemorySafeResourceManager Integration
      ✓ should integrate properly with MemorySafeResourceManager (6 ms)
      ✓ should handle resource lifecycle properly (2 ms)
      ✓ should handle shared resource scenarios (2 ms)
    Logging Integration
      ✓ should implement ILoggingService interface correctly (2 ms)
      ✓ should integrate logging with buffer operations (2 ms)
    Error Handling Integration
      ✓ should handle operations on uninitialized buffer (1 ms)
      ✓ should handle invalid input gracefully (3 ms)
      ✓ should recover from temporary errors (1 ms)
    End-to-End Workflow Scenarios
      ✓ should handle complete lifecycle workflow (2 ms)
      ✓ should handle concurrent workflow scenarios (9 ms)
      ✓ should handle stress workflow with recovery (5 ms)
    Cross-Component Integration
      ✓ should work with multiple buffer instances (2 ms)

Summary of all failing tests
 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts
  ● Test suite failed to run

    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:16:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'createEnhancedCleanupCoordinator'.

    16   createEnhancedCleanupCoordinator,
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:17:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'getEnhancedCleanupCoordinator'.

    17   getEnhancedCleanupCoordinator,
         ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:18:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'IDependencyGraph'.

    18   IDependencyGraph,
         ~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:19:3 - error TS2305: Module '"../CleanupCoordinatorEnhanced"' has no exported member 'IDependencyAnalysis'.

    19   IDependencyAnalysis
         ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:147:19 - error TS2339: Property 'registerCleanupOperation' does not exist on type 'CleanupCoordinatorEnhanced'.

    147       coordinator.registerCleanupOperation(name, operation);
                          ~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:370:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    370       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:376:42 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    376       const optimizedOrder = coordinator.optimizeOperationOrder(operations);
                                                 ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:462:56 - error TS2554: Expected 0 arguments, but got 1.

    462       const memoryTemplates = coordinator.getTemplates({ tags: ['memory'] });
                                                               ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:467:58 - error TS2554: Expected 0 arguments, but got 1.

    467       const resourceTemplates = coordinator.getTemplates({
                                                                 ~
    468         operationType: CleanupOperationType.RESOURCE_CLEANUP
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    469       });
        ~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:474:57 - error TS2554: Expected 0 arguments, but got 1.

    474       const author1Templates = coordinator.getTemplates({ author: 'author1' });
                                                                ~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:479:60 - error TS2554: Expected 0 arguments, but got 1.

    479       const memoryNameTemplates = coordinator.getTemplates({ namePattern: 'memory' });
                                                                   ~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:521:41 - error TS2551: Property 'getTemplateMetrics' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'getTemplates'?

    521       const metricsBefore = coordinator.getTemplateMetrics('metrics-template');
                                                ~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:185:10
        185   public getTemplates(): ICleanupTemplate[] {
                     ~~~~~~~~~~~~
        'getTemplates' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:525:40 - error TS2551: Property 'executeTemplateMinimal' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'executeTemplate'?

    525       const result = await coordinator.executeTemplateMinimal(
                                               ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:145:16
        145   public async executeTemplate(
                           ~~~~~~~~~~~~~~~
        'executeTemplate' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:536:40 - error TS2551: Property 'getTemplateMetrics' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'getTemplates'?

    536       const metricsAfter = coordinator.getTemplateMetrics('metrics-template');
                                               ~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:185:10
        185   public getTemplates(): ICleanupTemplate[] {
                     ~~~~~~~~~~~~
        'getTemplates' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:564:33 - error TS2339: Property 'buildDependencyGraph' does not exist on type 'CleanupCoordinatorEnhanced'.

    564       const graph = coordinator.buildDependencyGraph(operations);
                                        ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:602:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    602       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:655:33 - error TS2339: Property 'buildDependencyGraph' does not exist on type 'CleanupCoordinatorEnhanced'.

    655       const graph = coordinator.buildDependencyGraph(operations);
                                        ~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:663:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    663       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:706:42 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    706       const optimizedOrder = coordinator.optimizeOperationOrder(operations);
                                                 ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:741:32 - error TS2339: Property 'optimizeOperationOrder' does not exist on type 'CleanupCoordinatorEnhanced'.

    741       expect(() => coordinator.optimizeOperationOrder(operations)).toThrow(/circular dependencies/i);
                                       ~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:788:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    788       const analysis = coordinator.analyzeDependencies(operations);
                                           ~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:817:83 - error TS2554: Expected 1-2 arguments, but got 3.

    817       const checkpointId = await coordinator.createCheckpoint(operationId, state, rollbackActions);
                                                                                          ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:845:83 - error TS2554: Expected 1-2 arguments, but got 3.

    845       const checkpointId = await coordinator.createCheckpoint(operationId, state, rollbackActions);
                                                                                          ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:924:58 - error TS2554: Expected 0 arguments, but got 1.

    924       const op1Checkpoints = coordinator.listCheckpoints({ operationId: operationId1 });
                                                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:929:61 - error TS2554: Expected 0 arguments, but got 1.

    929       const recentCheckpoints = coordinator.listCheckpoints({ since: now });
                                                                    ~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:932:61 - error TS2554: Expected 0 arguments, but got 1.

    932       const futureCheckpoints = coordinator.listCheckpoints({
                                                                    ~
    933         since: new Date(Date.now() + 10000)
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    934       });
        ~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:989:80 - error TS2554: Expected 1-2 arguments, but got 3.

    989       const checkpointId = await coordinator.createCheckpoint(operationId, {}, rollbackActions);
                                                                                       ~~~~~~~~~~~~~~~
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1081:40 - error TS2551: Property 'executeTemplateMinimal' does not exist on type 'CleanupCoordinatorEnhanced'. Did you mean 'executeTemplate'?

    1081       const result = await coordinator.executeTemplateMinimal(
                                                ~~~~~~~~~~~~~~~~~~~~~~

      shared/src/base/CleanupCoordinatorEnhanced.ts:145:16
        145   public async executeTemplate(
                           ~~~~~~~~~~~~~~~
        'executeTemplate' is declared here.
    shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1109:36 - error TS2339: Property 'analyzeDependencies' does not exist on type 'CleanupCoordinatorEnhanced'.

    1109       const analysis = coordinator.analyzeDependencies(operations);
                                            ~~~~~~~~~~~~~~~~~~~

 FAIL  shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts (522 MB heap size)
  ● TemplateWorkflows › Basic Workflow Execution › should update execution metrics during workflow

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      205 |
      206 |       expect(execution.metrics.executedSteps).toBeGreaterThan(0);
    > 207 |       expect(execution.metrics.totalExecutionTime).toBeGreaterThan(0);
          |                                                    ^
      208 |     });
      209 |
      210 |     it('should generate unique execution IDs', () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:207:52)


Test Suites: 2 failed, 23 passed, 25 total
Tests:       1 failed, 566 passed, 567 total
Snapshots:   0 total
Time:        7.055 s, estimated 10 s
Ran all test suites matching /shared\/src\/base\/__tests__/i