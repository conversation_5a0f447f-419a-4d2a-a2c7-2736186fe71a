oa-prod$ timeout 30s npm test -- --testPathPattern="RollbackManager" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=RollbackManager --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts (249 MB heap size)
  RollbackManager
    Initialization and Configuration
      ✓ should initialize with default configuration (5 ms)
      ✓ should initialize with custom configuration (3 ms)
      ✓ should handle disabled rollback system (21 ms)
    Checkpoint Creation
      ✕ should create checkpoint with state only (7 ms)
      ✕ should create checkpoint with rollback actions (2 ms)
      ✓ should handle checkpoint creation errors gracefully (2 ms)
    Checkpoint Management
      ✕ should list all checkpoints (2 ms)
      ✕ should filter checkpoints by operation ID (2 ms)
      ✕ should find checkpoint by ID in list (2 ms)
      ✕ should return empty list for non-existent operation (2 ms)
      ✕ should cleanup old checkpoints (2 ms)
    Rollback Execution
      ✕ should execute rollback successfully (2 ms)
      ✕ should handle rollback failures gracefully (2 ms)
      ✕ should rollback to most recent checkpoint for operation (1 ms)
      ✕ should handle rollback of non-existent checkpoint (6 ms)
      ✕ should handle rollback when no checkpoints exist for operation (1 ms)
    Rollback Validation
      ✕ should validate rollback capability (1 ms)
      ✓ should detect rollback issues (2 ms)
    Checkpoint Cleanup
      ✕ should cleanup old checkpoints when limit exceeded (2 ms)
      ✕ should cleanup checkpoints by operation (2 ms)
    Error Handling and Edge Cases
      ✕ should handle concurrent checkpoint operations (2 ms)
      ✕ should handle large state objects (1 ms)
      ✕ should handle shutdown gracefully (1 ms)
    Performance and Metrics
      ✕ should track checkpoint creation performance (2 ms)
      ✕ should track rollback execution performance (2 ms)

  ● RollbackManager › Checkpoint Creation › should create checkpoint with state only

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:85:28)

  ● RollbackManager › Checkpoint Creation › should create checkpoint with rollback actions

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:111:28)

  ● RollbackManager › Checkpoint Management › should list all checkpoints

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:141:7)

  ● RollbackManager › Checkpoint Management › should filter checkpoints by operation ID

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:141:7)

  ● RollbackManager › Checkpoint Management › should find checkpoint by ID in list

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:141:7)

  ● RollbackManager › Checkpoint Management › should return empty list for non-existent operation

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:141:7)

  ● RollbackManager › Checkpoint Management › should cleanup old checkpoints

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:141:7)

  ● RollbackManager › Rollback Execution › should execute rollback successfully

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:200:22)

  ● RollbackManager › Rollback Execution › should handle rollback failures gracefully

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:200:22)

  ● RollbackManager › Rollback Execution › should rollback to most recent checkpoint for operation

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:200:22)

  ● RollbackManager › Rollback Execution › should handle rollback of non-existent checkpoint

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:200:22)

  ● RollbackManager › Rollback Execution › should handle rollback when no checkpoints exist for operation

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:200:22)

  ● RollbackManager › Rollback Validation › should validate rollback capability

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:270:7)

  ● RollbackManager › Checkpoint Cleanup › should cleanup old checkpoints when limit exceeded

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:300:7)

  ● RollbackManager › Checkpoint Cleanup › should cleanup checkpoints by operation

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:316:7)

  ● RollbackManager › Error Handling and Edge Cases › should handle concurrent checkpoint operations

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
          at async Promise.all (index 0)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:338:29)

  ● RollbackManager › Error Handling and Edge Cases › should handle large state objects

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:349:28)

  ● RollbackManager › Error Handling and Edge Cases › should handle shutdown gracefully

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:358:7)

  ● RollbackManager › Performance and Metrics › should track checkpoint creation performance

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:368:7)

  ● RollbackManager › Performance and Metrics › should track rollback execution performance

    TypeError: (0 , RollbackUtilities_1.deepClone) is not a function

      175 |         operationId,
      176 |         timestamp: new Date(),
    > 177 |         state: cleanState ? deepClone(cleanState) : null,
          |                                      ^
      178 |         rollbackActions: [...finalRollbackActions],
      179 |         metadata: {
      180 |           systemState: await captureSystemState(),

      at RollbackManager.createCheckpoint (shared/src/base/modules/cleanup/RollbackManager.ts:177:38)
      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/RollbackManager.test.ts:377:28)

Test Suites: 1 failed, 1 total
Tests:       20 failed, 5 passed, 25 total
Snapshots:   0 total
Time:        2.356 s, estimated 4 s
Ran all test suites matching /RollbackManager/i.
