oa-prod$ npm test -- --testPathPattern="shared/src/base/__tests__/modules/cleanup" --silent | grep -E "(PASS|FAIL|Tests:|failed|passed)"
FAIL shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts (399 MB heap size)
  CleanupTemplateManager
    Template Registration
      ✓ should register valid template successfully (8 ms)
      ✓ should validate template structure during registration (18 ms)
      ✓ should detect circular dependencies in template operations (3 ms)
      ✓ should initialize template metrics upon registration (3 ms)
    Template Execution
      ✕ should execute template with target components (7 ms)
      ✕ should handle template execution errors gracefully (3 ms)
      ✓ should generate unique execution IDs (8 ms)
      ✓ should reject execution of non-existent template (4 ms)
    Template Metrics
      ✕ should collect execution metrics during template execution (4 ms)
      ✓ should return empty metrics for non-existent template (2 ms)
      ✓ should return all template metrics when no template ID specified (2 ms)
    Module Integration
      ✓ should integrate with TemplateValidator for validation (2 ms)
      ✓ should integrate with TemplateWorkflowExecutor for execution (2 ms)
      ✓ should integrate with DependencyGraph for dependency management (2 ms)
    Performance & Memory Safety
      ✓ should maintain performance requirements during template operations (2 ms)
      ✓ should properly cleanup resources during shutdown (11 ms)

  ● CleanupTemplateManager › Template Execution › should execute template with target components

    expect(received).toBe(expected) // Object.is equality

    Expected: "success"
    Received: "failure"

      273 |       expect(executionTime).toBeLessThan(100); // <100ms requirement
      274 |
    > 275 |       expect(result.status).toBe('success');
          |                             ^
      276 |       expect(result.templateId).toBe('test-template-001');
      277 |       expect(result.executedSteps).toBeGreaterThan(0);
      278 |       expect(result.errors).toHaveLength(0);

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:275:29)

  ● CleanupTemplateManager › Template Execution › should handle template execution errors gracefully

    expect(received).toBe(expected) // Object.is equality

    Expected: "failure"
    Received: "success"

      289 |       );
      290 |
    > 291 |       expect(result.status).toBe('failure');
          |                             ^
      292 |       expect(result.errors.length).toBeGreaterThan(0);
      293 |     });
      294 |

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:291:29)

  ● CleanupTemplateManager › Template Metrics › should collect execution metrics during template execution

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      342 |       const metrics = templateManager.getTemplateMetrics('test-template-001');
      343 |       expect(metrics.executedSteps).toBeGreaterThan(0);
    > 344 |       expect(metrics.totalExecutionTime).toBeGreaterThan(0);
          |                                          ^
      345 |       expect(metrics.averageStepTime).toBeGreaterThan(0);
      346 |     });
      347 |

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/CleanupTemplateManager.test.ts:344:42)

FAIL shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts (415 MB heap size)
  TemplateWorkflows
    Basic Workflow Execution
      ✓ should execute simple workflow successfully (3 ms)
      ✓ should handle empty workflow gracefully (2 ms)
      ✓ should update execution metrics during workflow (2 ms)
      ✓ should generate unique execution IDs (5 ms)
    Parallel & Sequential Execution
      ✓ should execute parallel workflow with independent steps (2 ms)
      ✓ should execute sequential workflow with dependencies (2 ms)
      ✓ should respect workflow configuration settings (1 ms)
      ✓ should allow configuration updates (1 ms)
    Retry Logic & Error Handling
      ✕ should retry failed steps according to configuration (2 ms)
      ✕ should handle component registry failures gracefully (2 ms)
      ✓ should execute dry run mode without actual operations (3 ms)
      ✓ should skip conditions when specified in options (6 ms)
    Component Integration & Simulation
      ✓ should integrate with component registry for component discovery (2 ms)
      ✓ should simulate different operation types correctly (3 ms)
      ✓ should handle component overrides in execution options (1 ms)
    Utility Functions & Performance
      ✓ should create workflow executor with factory function (1 ms)
      ✓ should execute workflow using utility function (2 ms)
      ✓ should maintain performance requirements for complex workflows (3 ms)

  ● TemplateWorkflows › Retry Logic & Error Handling › should retry failed steps according to configuration

    expect(received).toBeGreaterThan(expected)

    Expected: > 0
    Received:   0

      351 |
      352 |       expect(results).toHaveLength(1); // One result per step
    > 353 |       expect(results[0].retryCount).toBeGreaterThan(0);
          |                                     ^
      354 |     });
      355 |
      356 |     it('should handle component registry failures gracefully', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:353:37)

  ● TemplateWorkflows › Retry Logic & Error Handling › should handle component registry failures gracefully

    expect(received).toBeDefined()

    Received: undefined

      364 |       expect(results).toHaveLength(1); // One result per step
      365 |       expect(results[0].success).toBe(false);
    > 366 |       expect(results[0].error).toBeDefined();
          |                                ^
      367 |     });
      368 |
      369 |     it('should execute dry run mode without actual operations', async () => {

      at Object.<anonymous> (shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts:366:32)

PASS shared/src/base/__tests__/modules/cleanup/TemplateDependencies.test.ts (420 MB heap size)
  TemplateDependencies
    Basic Graph Operations
      ✓ should add nodes to the graph (2 ms)
      ✓ should add edges between nodes (2 ms)
      ✓ should add dependencies correctly (2 ms)
      ✓ should handle duplicate node additions gracefully (2 ms)
      ✓ should clear the graph completely (1 ms)
    Cycle Detection
      ✓ should detect no cycles in acyclic graph (2 ms)
      ✓ should detect simple two-node cycle (1 ms)
      ✓ should detect complex multi-node cycle (3 ms)
      ✓ should find all cycles in graph (1 ms)
      ✓ should handle self-referencing nodes (1 ms)
    Topological Sorting
      ✓ should perform topological sort on simple DAG (2 ms)
      ✓ should handle complex dependency relationships (2 ms)
      ✓ should return empty array for cyclic graph (2 ms)
      ✓ should handle disconnected components (2 ms)
    Critical Path & Parallel Analysis
      ✓ should calculate critical path correctly (2 ms)
      ✓ should identify parallel execution groups (1 ms)
      ✓ should calculate graph metrics accurately (1 ms)
      ✓ should detect transitive dependencies (2 ms)
    Utility Functions & Performance
      ✓ should create dependency graph from operations (1 ms)
      ✓ should validate dependency graph for issues (2 ms)
      ✓ should detect validation issues in problematic graphs (4 ms)
      ✓ should clone dependency graph accurately (2 ms)
      ✓ should maintain performance requirements for complex graphs (2 ms)
      ✓ should handle edge cases gracefully (3 ms)

PASS shared/src/base/__tests__/modules/cleanup/TemplateValidation.test.ts (436 MB heap size)
  TemplateValidation
    Template Structure Validation
      ✓ should validate correct template structure (2 ms)
      ✓ should reject template with missing ID (1 ms)
      ✓ should reject template with no operations (2 ms)
      ✓ should warn about missing description (2 ms)
      ✓ should validate operation structure completeness (2 ms)
    Dependency Validation
      ✓ should validate correct dependency relationships (1 ms)
      ✓ should detect invalid dependency references (2 ms)
      ✓ should detect circular dependencies (3 ms)
    Condition Evaluation
      ✓ should evaluate always condition correctly (1 ms)
      ✓ should evaluate on_success condition correctly (5 ms)
      ✓ should evaluate on_failure condition correctly (2 ms)
      ✓ should evaluate component_exists condition correctly (2 ms)
      ✓ should validate custom condition requirements (2 ms)
    Quality Scoring & Utilities
      ✓ should calculate quality score based on template completeness (2 ms)
      ✓ should penalize quality score for validation issues (1 ms)
      ✓ should match components using regex patterns (2 ms)
      ✓ should handle invalid regex patterns gracefully (1 ms)
      ✓ should use validateTemplate utility function (2 ms)
    Performance & Extended Validation
      ✓ should maintain performance requirements during validation (1 ms)
      ✓ should provide comprehensive extended validation results (2 ms)

Test Suites: 2 failed, 2 passed, 4 total
Tests:       5 failed, 73 passed, 78 total
Snapshots:   0 total
Time:        4.198 s, estimated 5 s
