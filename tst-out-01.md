npm test -- --testPathPattern="CleanupCoordinatorEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=CleanupCoordinatorEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts (14.141 s, 244 MB heap size)
  CleanupCoordinatorEnhanced
    Cleanup Templates System
      ✓ should register and validate cleanup templates (7 ms)
      ✓ should validate template structure and detect issues (50 ms)
      ✓ should execute templates with dependency resolution (5 ms)
      ✓ should filter templates by criteria (8 ms)
      ✕ should track template execution metrics (5007 ms)
    Advanced Dependency Resolution
      ✓ should create dependency graph without hanging (3 ms)
      ✓ should detect circular dependencies (4 ms)
      ✓ should build and analyze dependency graphs (4 ms)
      ✓ should optimize operation execution order (3 ms)
      ✓ should throw error for circular dependencies in optimization (3 ms)
      ✓ should identify bottlenecks and optimization opportunities (3 ms)
    Rollback and Recovery System
      ✓ should create and manage checkpoints (10 ms)
      ✓ should rollback to checkpoint successfully (4 ms)
      ✓ should rollback operation using most recent checkpoint (6 ms)
      ✓ should validate rollback capability (3 ms)
      ✓ should filter checkpoints by criteria (3 ms)
      ✕ should cleanup old checkpoints (4006 ms)
      ✓ should handle rollback failures gracefully (5 ms)
    Integration and Performance
      ✓ should maintain backward compatibility with base CleanupCoordinator (4 ms)
      ✕ should handle template execution within performance requirements (3005 ms)
      ✓ should handle dependency analysis within performance requirements (4 ms)
      ✓ should handle checkpoint creation within performance requirements (5 ms)
    Factory Functions
      ✓ should create enhanced cleanup coordinator via factory function (2 ms)
      ✓ should get enhanced cleanup coordinator via getter function (2 ms)
    Error Handling and Edge Cases
      ✓ should handle template execution with non-existent template (2 ms)
      ✓ should handle rollback with non-existent checkpoint (3 ms)
      ✓ should handle rollback with no checkpoints for operation (2 ms)
      ✓ should handle disabled rollback system (2 ms)
      ✓ should handle empty template operations (3 ms)
      ✓ should handle malformed component patterns (7 ms)

  ● CleanupCoordinatorEnhanced › Cleanup Templates System › should track template execution metrics

    thrown: "Exceeded timeout of 5000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      485 |     });
      486 |
    > 487 |     it('should track template execution metrics', async () => {
          |     ^
      488 |       const template = {
      489 |         id: 'metrics-template',
      490 |         name: 'Metrics Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:487:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:192:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Rollback and Recovery System › should cleanup old checkpoints

    thrown: "Exceeded timeout of 4000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      951 |     });
      952 |
    > 953 |     it('should cleanup old checkpoints', async () => {
          |     ^
      954 |       const operationId = 'test-operation-7';
      955 |
      956 |       // ✅ Create actual checkpoints with real data

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:953:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:816:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

  ● CleanupCoordinatorEnhanced › Integration and Performance › should handle template execution within performance requirements

    thrown: "Exceeded timeout of 3000 ms for a test.
    Add a timeout value to this test to increase the timeout, if this is a long-running test. See https://jestjs.io/docs/api#testname-fn-timeout."

      1060 |     }, 5000); // 5 second Jest timeout
      1061 |
    > 1062 |     it('should handle template execution within performance requirements', async () => {
           |     ^
      1063 |       const template = {
      1064 |         id: 'performance-template',
      1065 |         name: 'Performance Test Template',

      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1062:5
      at shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:1026:3
      at Object.<anonymous> (shared/src/base/__tests__/CleanupCoordinatorEnhanced.test.ts:31:1)

Test Suites: 1 failed, 1 total
Tests:       3 failed, 27 passed, 30 total
Snapshots:   0 total
Time:        14.401 s
Ran all test suites matching /CleanupCoordinatorEnhanced.test.ts/i.
