/**
 * ============================================================================
 * AI CONTEXT: Enterprise Error Handling - Production-Grade Resilience Patterns
 * Purpose: Circuit breaker, exponential backoff, and enterprise error recovery
 * Complexity: Complex - Multi-pattern error handling with state management
 * AI Navigation: 5 logical sections, 2 major domains (Circuit Breaker, Retry Logic)
 * ============================================================================
 */

/**
 * Document Type: Enterprise Utility Module
 * Version: 1.0.0
 * Created: 2025-07-25
 * Authority: President & CEO, E.Z. Consultancy
 * Classification: Core Infrastructure - Production Resilience
 */

// ============================================================================
// SECTION 1: IMPORTS & DEPENDENCIES (Lines 1-30)
// AI Context: "Core dependencies for enterprise error handling"
// ============================================================================

import { performance } from 'perf_hooks';
import { JestCompatibilityUtils } from './JestCompatibilityUtils';

// ============================================================================
// SECTION 2: TYPE DEFINITIONS (Lines 31-120)
// AI Context: "Enterprise error handling interfaces and types"
// ============================================================================

/**
 * Circuit breaker states
 */
export enum CircuitBreakerState {
  CLOSED = 'closed',     // Normal operation
  OPEN = 'open',         // Failing fast
  HALF_OPEN = 'half-open' // Testing recovery
}

/**
 * Enterprise retry configuration with circuit breaker
 */
export interface IEnterpriseRetryConfig {
  /** Maximum number of retry attempts */
  maxRetries: number;
  /** Base delay between retries (ms) */
  baseDelayMs: number;
  /** Maximum delay between retries (ms) */
  maxDelayMs: number;
  /** Enable exponential backoff */
  exponentialBackoff: boolean;
  /** Enable jitter to prevent thundering herd */
  jitterEnabled: boolean;
  /** Circuit breaker failure threshold */
  circuitBreakerThreshold: number;
  /** Circuit breaker timeout (ms) */
  circuitBreakerTimeoutMs: number;
  /** Circuit breaker recovery attempts */
  circuitBreakerRecoveryAttempts: number;
}

/**
 * Error classification for enterprise handling
 */
export interface IErrorClassification {
  /** Error type classification */
  type: 'transient' | 'permanent' | 'timeout' | 'resource' | 'authentication' | 'unknown';
  /** Whether error is retryable */
  retryable: boolean;
  /** Recommended retry delay (ms) */
  recommendedDelay: number;
  /** Error severity level */
  severity: 'low' | 'medium' | 'high' | 'critical';
  /** Recovery strategy */
  recoveryStrategy: 'retry' | 'fallback' | 'circuit-breaker' | 'escalate';
}

/**
 * Circuit breaker metrics
 */
export interface ICircuitBreakerMetrics {
  /** Current state */
  state: CircuitBreakerState;
  /** Failure count */
  failureCount: number;
  /** Success count */
  successCount: number;
  /** Last failure time */
  lastFailureTime: number;
  /** Last success time */
  lastSuccessTime: number;
  /** Total requests */
  totalRequests: number;
  /** Failure rate (0-1) */
  failureRate: number;
}

/**
 * Enterprise retry result
 */
export interface IEnterpriseRetryResult<T> {
  /** Operation result */
  result?: T;
  /** Whether operation succeeded */
  success: boolean;
  /** Number of attempts made */
  attempts: number;
  /** Total execution time (ms) */
  executionTime: number;
  /** Final error if failed */
  error?: Error;
  /** Circuit breaker triggered */
  circuitBreakerTriggered: boolean;
  /** Error classification */
  errorClassification?: IErrorClassification;
}

// ============================================================================
// SECTION 3: CONSTANTS & CONFIGURATION (Lines 121-150)
// AI Context: "Default configuration values for enterprise error handling"
// ============================================================================

/**
 * Default enterprise retry configuration
 */
const DEFAULT_ENTERPRISE_CONFIG: IEnterpriseRetryConfig = {
  maxRetries: 3,
  baseDelayMs: 1000,
  maxDelayMs: 30000,
  exponentialBackoff: true,
  jitterEnabled: true,
  circuitBreakerThreshold: 5,
  circuitBreakerTimeoutMs: 60000,
  circuitBreakerRecoveryAttempts: 3
};

/**
 * Error classification patterns
 */
const ERROR_PATTERNS = {
  TRANSIENT: /timeout|network|connection|temporary/i,
  RESOURCE: /memory|disk|cpu|quota|limit/i,
  AUTHENTICATION: /auth|unauthorized|forbidden|token/i,
  PERMANENT: /not found|invalid|malformed|syntax/i
};

// ============================================================================
// SECTION 4: CIRCUIT BREAKER IMPLEMENTATION (Lines 151-250)
// AI Context: "Circuit breaker pattern for fault tolerance"
// ============================================================================

/**
 * Enterprise circuit breaker implementation
 * LESSON LEARNED: Prevent cascading failures in distributed systems
 */
export class CircuitBreaker {
  private _state: CircuitBreakerState = CircuitBreakerState.CLOSED;
  private _failureCount: number = 0;
  private _successCount: number = 0;
  private _lastFailureTime: number = 0;
  private _lastSuccessTime: number = 0;
  private _totalRequests: number = 0;
  private _config: IEnterpriseRetryConfig;

  constructor(config: Partial<IEnterpriseRetryConfig> = {}) {
    this._config = { ...DEFAULT_ENTERPRISE_CONFIG, ...config };
  }

  /**
   * Check if operation should be allowed
   */
  canExecute(): boolean {
    switch (this._state) {
      case CircuitBreakerState.CLOSED:
        return true;
      case CircuitBreakerState.OPEN:
        return this._shouldAttemptRecovery();
      case CircuitBreakerState.HALF_OPEN:
        return this._successCount < this._config.circuitBreakerRecoveryAttempts;
      default:
        return false;
    }
  }

  /**
   * Record successful operation
   */
  recordSuccess(): void {
    this._successCount++;
    this._totalRequests++;
    this._lastSuccessTime = performance.now();

    if (this._state === CircuitBreakerState.HALF_OPEN) {
      if (this._successCount >= this._config.circuitBreakerRecoveryAttempts) {
        this._state = CircuitBreakerState.CLOSED;
        this._failureCount = 0;
      }
    }
  }

  /**
   * Record failed operation
   */
  recordFailure(): void {
    this._failureCount++;
    this._totalRequests++;
    this._lastFailureTime = performance.now();

    if (this._state === CircuitBreakerState.CLOSED) {
      if (this._failureCount >= this._config.circuitBreakerThreshold) {
        this._state = CircuitBreakerState.OPEN;
      }
    } else if (this._state === CircuitBreakerState.HALF_OPEN) {
      this._state = CircuitBreakerState.OPEN;
      this._successCount = 0;
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): ICircuitBreakerMetrics {
    return {
      state: this._state,
      failureCount: this._failureCount,
      successCount: this._successCount,
      lastFailureTime: this._lastFailureTime,
      lastSuccessTime: this._lastSuccessTime,
      totalRequests: this._totalRequests,
      failureRate: this._totalRequests > 0 ? this._failureCount / this._totalRequests : 0
    };
  }

  /**
   * Reset circuit breaker state
   */
  reset(): void {
    this._state = CircuitBreakerState.CLOSED;
    this._failureCount = 0;
    this._successCount = 0;
    this._totalRequests = 0;
  }

  /**
   * Check if should attempt recovery
   */
  private _shouldAttemptRecovery(): boolean {
    const timeSinceLastFailure = performance.now() - this._lastFailureTime;
    if (timeSinceLastFailure >= this._config.circuitBreakerTimeoutMs) {
      this._state = CircuitBreakerState.HALF_OPEN;
      this._successCount = 0;
      return true;
    }
    return false;
  }
}

// ============================================================================
// SECTION 5: ENTERPRISE ERROR HANDLING UTILITIES (Lines 251-300)
// AI Context: "Main enterprise error handling implementation"
// ============================================================================

/**
 * Enterprise error handling utilities
 * LESSON LEARNED: Production-grade error handling with multiple resilience patterns
 */
export class EnterpriseErrorHandler {
  private static _circuitBreakers = new Map<string, CircuitBreaker>();

  /**
   * Classify error for appropriate handling
   */
  static classifyError(error: Error): IErrorClassification {
    const message = error.message.toLowerCase();

    if (ERROR_PATTERNS.TRANSIENT.test(message)) {
      return {
        type: 'transient',
        retryable: true,
        recommendedDelay: 1000,
        severity: 'medium',
        recoveryStrategy: 'retry'
      };
    }

    if (ERROR_PATTERNS.RESOURCE.test(message)) {
      return {
        type: 'resource',
        retryable: true,
        recommendedDelay: 5000,
        severity: 'high',
        recoveryStrategy: 'circuit-breaker'
      };
    }

    if (ERROR_PATTERNS.AUTHENTICATION.test(message)) {
      return {
        type: 'authentication',
        retryable: false,
        recommendedDelay: 0,
        severity: 'critical',
        recoveryStrategy: 'escalate'
      };
    }

    if (ERROR_PATTERNS.PERMANENT.test(message)) {
      return {
        type: 'permanent',
        retryable: false,
        recommendedDelay: 0,
        severity: 'high',
        recoveryStrategy: 'fallback'
      };
    }

    return {
      type: 'unknown',
      retryable: true,
      recommendedDelay: 2000,
      severity: 'medium',
      recoveryStrategy: 'retry'
    };
  }

  /**
   * Get or create circuit breaker for operation
   */
  static getCircuitBreaker(operationId: string, config?: Partial<IEnterpriseRetryConfig>): CircuitBreaker {
    if (!this._circuitBreakers.has(operationId)) {
      this._circuitBreakers.set(operationId, new CircuitBreaker(config));
    }
    return this._circuitBreakers.get(operationId)!;
  }

  /**
   * Execute operation with enterprise retry logic
   * LESSON LEARNED: Comprehensive error handling with circuit breaker and exponential backoff
   */
  static async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationId: string,
    config: Partial<IEnterpriseRetryConfig> = {}
  ): Promise<IEnterpriseRetryResult<T>> {
    const finalConfig = { ...DEFAULT_ENTERPRISE_CONFIG, ...config };
    const circuitBreaker = this.getCircuitBreaker(operationId, finalConfig);
    const startTime = performance.now();

    let lastError: Error;
    let attempts = 0;
    let circuitBreakerTriggered = false;

    for (let attempt = 1; attempt <= finalConfig.maxRetries + 1; attempt++) {
      attempts = attempt;

      // Check circuit breaker
      if (!circuitBreaker.canExecute()) {
        circuitBreakerTriggered = true;
        lastError = new Error(`Circuit breaker open for operation: ${operationId}`);
        break;
      }

      try {
        // Execute operation
        const result = await operation();

        // Record success
        circuitBreaker.recordSuccess();

        return {
          result,
          success: true,
          attempts,
          executionTime: performance.now() - startTime,
          circuitBreakerTriggered: false
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Classify error
        const classification = this.classifyError(lastError);

        // Record failure
        circuitBreaker.recordFailure();

        // Check if should retry
        if (attempt <= finalConfig.maxRetries && classification.retryable) {
          // Calculate delay with exponential backoff and jitter
          const delay = this._calculateRetryDelay(attempt, finalConfig, classification);

          // Jest-compatible delay
          await JestCompatibilityUtils.compatibleDelay(delay);
        }
      }
    }

    return {
      success: false,
      attempts,
      executionTime: performance.now() - startTime,
      error: lastError,
      circuitBreakerTriggered,
      errorClassification: this.classifyError(lastError)
    };
  }

  /**
   * Calculate retry delay with exponential backoff and jitter
   * LESSON LEARNED: Prevent thundering herd with jitter
   */
  private static _calculateRetryDelay(
    attempt: number,
    config: IEnterpriseRetryConfig,
    classification: IErrorClassification
  ): number {
    let delay = config.baseDelayMs;

    // Use error-specific delay if available
    if (classification.recommendedDelay > 0) {
      delay = Math.max(delay, classification.recommendedDelay);
    }

    // Apply exponential backoff
    if (config.exponentialBackoff) {
      delay = Math.min(delay * Math.pow(2, attempt - 1), config.maxDelayMs);
    }

    // Apply jitter to prevent thundering herd
    if (config.jitterEnabled) {
      const jitter = delay * 0.1 * Math.random(); // ±10% jitter
      delay = delay + (Math.random() > 0.5 ? jitter : -jitter);
    }

    return Math.max(delay, 0);
  }

  /**
   * Reset all circuit breakers
   */
  static resetAllCircuitBreakers(): void {
    this._circuitBreakers.forEach(cb => cb.reset());
    this._circuitBreakers.clear();
  }

  /**
   * Get metrics for all circuit breakers
   */
  static getAllCircuitBreakerMetrics(): Map<string, ICircuitBreakerMetrics> {
    const metrics = new Map<string, ICircuitBreakerMetrics>();
    this._circuitBreakers.forEach((cb, id) => {
      metrics.set(id, cb.getMetrics());
    });
    return metrics;
  }
}
