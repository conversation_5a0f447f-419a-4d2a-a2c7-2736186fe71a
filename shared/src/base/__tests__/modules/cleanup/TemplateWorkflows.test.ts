/**
 * @file Template Workflows Test Suite
 * @filepath shared/src/base/__tests__/modules/cleanup/TemplateWorkflows.test.ts
 * @task-id M-TSK-01.SUB-01.REF-01.TEMPLATE-WORKFLOWS-TESTS
 * @component template-workflows-tests
 * @created 2025-07-25 02:49:40 +03
 * 
 * @description
 * Comprehensive test suite for TemplateWorkflows providing validation of:
 * - TemplateWorkflowExecutor functionality and configuration
 * - Parallel and sequential workflow execution strategies
 * - Step execution with retry logic and timeout handling
 * - Component registry integration and component-specific execution
 * - Error handling, rollback scenarios, and recovery workflows
 * - Performance metrics, simulation patterns, and timing validation
 * 
 * PHASE B COMPLIANCE:
 * - 100% test preservation mandate from refactoring plan
 * - Performance requirements: <100ms workflow execution
 * - Jest compatibility with proven Phase 5 async yielding patterns
 * - File size target: ≤400 lines per refactoring specifications
 * 
 * LESSONS LEARNED INTEGRATION:
 * - Jest compatibility: Async yielding instead of setTimeout patterns
 * - Memory safety: Proper workflow executor cleanup and resource management
 * - Performance: Optimized workflow execution testing with timing validation
 */

import { 
  TemplateWorkflowExecutor,
  createWorkflowExecutor,
  executeTemplateWorkflow,
  IWorkflowExecutionConfig,
  IStepExecutionOptions,
  DEFAULT_WORKFLOW_CONFIG
} from '../../../modules/cleanup/TemplateWorkflows';
import {
  ICleanupTemplate,
  ITemplateExecution,
  IStepExecutionResult,
  IComponentRegistry
} from '../../../types/CleanupTypes';
import { CleanupOperationType, CleanupPriority } from '../../../CleanupCoordinator';

/**
 * ============================================================================
 * AI CONTEXT: Template Workflows Test Suite
 * Purpose: Comprehensive testing of workflow execution functionality
 * Complexity: Complex - Multi-step execution with dependency management
 * AI Navigation: 6 logical sections - Setup, Basic Execution, Parallel/Sequential, Retry Logic, Integration, Performance
 * ============================================================================
 */

/**
 * ============================================================================
 * SECTION 1: TEST SETUP & UTILITIES (Lines 1-80)
 * AI Context: "Test configuration, workflow executor setup, and helper functions"
 * ============================================================================
 */

describe('TemplateWorkflows', () => {
  let workflowExecutor: TemplateWorkflowExecutor;
  let mockComponentRegistry: IComponentRegistry;

  // Test configuration with Jest compatibility
  const testConfig: Partial<IWorkflowExecutionConfig> = {
    maxConcurrentSteps: 3,
    stepTimeoutMs: 5000,
    retryAttempts: 2,
    retryDelayMs: 100,
    enableParallelExecution: true,
    enableRollbackOnFailure: true,
    continueOnStepFailure: false,
    testMode: true
  };

  // Mock component registry for testing
  const createMockComponentRegistry = (): IComponentRegistry => ({
    findComponents: jest.fn().mockResolvedValue(['test-component-1', 'test-component-2']),
    getCleanupOperation: jest.fn().mockReturnValue(jest.fn().mockResolvedValue(true)),
    registerOperation: jest.fn().mockReturnValue(true),
    hasOperation: jest.fn().mockReturnValue(true),
    listOperations: jest.fn().mockReturnValue(['cleanup', 'validation']),
    getOperationMetrics: jest.fn().mockReturnValue({
      totalOperations: 5,
      successfulOperations: 4,
      failedOperations: 1,
      averageExecutionTime: 25
    })
  });

  // Helper to create test template
  const createTestTemplate = (overrides: Partial<ICleanupTemplate> = {}): ICleanupTemplate => ({
    id: 'test-workflow-template',
    name: 'Test Workflow Template',
    description: 'Template for workflow execution testing',
    version: '1.0.0',
    operations: [
      {
        id: 'step-001',
        type: CleanupOperationType.RESOURCE_CLEANUP,
        componentPattern: 'test-.*',
        operationName: 'cleanup-resources',
        parameters: { level: 'basic' },
        timeout: 3000,
        retryPolicy: {
          maxRetries: 2,
          retryDelay: 500,
          backoffMultiplier: 2,
          maxRetryDelay: 5000,
          retryOnErrors: ['TIMEOUT', 'NETWORK_ERROR']
        },
        dependsOn: [],
        priority: CleanupPriority.NORMAL,
        estimatedDuration: 1000,
        description: 'Basic resource cleanup'
      }
    ],
    conditions: [],
    rollbackSteps: [],
    metadata: { purpose: 'workflow-testing' },
    tags: ['test', 'workflow'],
    createdAt: new Date(),
    modifiedAt: new Date(),
    author: 'Test Suite',
    validationRules: [],
    ...overrides
  });

  // Helper to create template execution
  const createTestExecution = (templateId: string): ITemplateExecution => ({
    id: `exec-${Date.now()}`,
    templateId,
    targetComponents: ['test-component-1', 'test-component-2'],
    parameters: { executionMode: 'test' },
    status: 'running',
    startTime: new Date(),
    stepResults: new Map(),
    rollbackExecuted: false,
    metrics: {
      totalSteps: 1,
      executedSteps: 0,
      failedSteps: 0,
      skippedSteps: 0,
      averageStepTime: 0,
      longestStepTime: 0,
      dependencyResolutionTime: 0,
      validationTime: 0,
      totalExecutionTime: 0
    }
  });

  beforeEach(async () => {
    // LESSON LEARNED: Async yielding for Jest compatibility
    await Promise.resolve();
    
    mockComponentRegistry = createMockComponentRegistry();
    workflowExecutor = createWorkflowExecutor(mockComponentRegistry, testConfig);
  });

  afterEach(async () => {
    // LESSON LEARNED: Proper resource cleanup
    await Promise.resolve();
  });

  /**
   * ============================================================================
   * SECTION 2: BASIC WORKFLOW EXECUTION (Lines 81-160)
   * AI Context: "Basic workflow execution functionality and result processing"
   * ============================================================================
   */

  describe('Basic Workflow Execution', () => {
    it('should execute simple workflow successfully', async () => {
      // LESSON LEARNED: Performance timing validation
      const startTime = performance.now();
      
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(100); // <100ms requirement
      
      expect(results).toHaveLength(1);
      expect(results[0].stepId).toBe('step-001');
      expect(results[0].success).toBe(true);
    });

    it('should handle empty workflow gracefully', async () => {
      const template = createTestTemplate({ operations: [] });
      const execution = createTestExecution(template.id);
      
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      expect(results).toHaveLength(0);
    });

    it('should update execution metrics during workflow', async () => {
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      
      await workflowExecutor.executeWorkflow(template, execution);
      
      expect(execution.metrics.executedSteps).toBeGreaterThan(0);
      expect(execution.metrics.totalExecutionTime).toBeGreaterThan(0);
    });

    it('should generate unique execution IDs', () => {
      const id1 = workflowExecutor.generateExecutionId('template-1');
      const id2 = workflowExecutor.generateExecutionId('template-1');
      
      expect(id1).toBeDefined();
      expect(id2).toBeDefined();
      expect(id1).not.toBe(id2);
      expect(id1).toContain('template-1');
    });
  });

  /**
   * ============================================================================
   * SECTION 3: PARALLEL & SEQUENTIAL EXECUTION (Lines 161-240)
   * AI Context: "Parallel vs sequential execution strategies and dependency management"
   * ============================================================================
   */

  describe('Parallel & Sequential Execution', () => {
    it('should execute parallel workflow with independent steps', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'parallel-1',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-1',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Parallel step 1'
          },
          {
            id: 'parallel-2',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-2',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Parallel step 2'
          }
        ]
      });
      
      const execution = createTestExecution(template.id);
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.success)).toBe(true);
    });

    it('should execute sequential workflow with dependencies', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'step-first',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-first',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.HIGH,
            estimatedDuration: 500,
            description: 'First step'
          },
          {
            id: 'step-second',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-second',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: ['step-first'],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Second step'
          }
        ]
      });
      
      const execution = createTestExecution(template.id);
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      expect(results).toHaveLength(2);
      expect(execution.stepResults.has('step-first')).toBe(true);
      expect(execution.stepResults.has('step-second')).toBe(true);
    });

    it('should respect workflow configuration settings', () => {
      const config = workflowExecutor.getConfig();
      
      expect(config.maxConcurrentSteps).toBe(3);
      expect(config.enableParallelExecution).toBe(true);
      expect(config.testMode).toBe(true);
    });

    it('should allow configuration updates', () => {
      const newConfig = { maxConcurrentSteps: 5, retryAttempts: 3 };
      
      workflowExecutor.updateConfig(newConfig);
      const updatedConfig = workflowExecutor.getConfig();
      
      expect(updatedConfig.maxConcurrentSteps).toBe(5);
      expect(updatedConfig.retryAttempts).toBe(3);
    });
  });

  /**
   * ============================================================================
   * SECTION 4: RETRY LOGIC & ERROR HANDLING (Lines 241-320)
   * AI Context: "Step retry logic, error handling, and recovery scenarios"
   * ============================================================================
   */

  describe('Retry Logic & Error Handling', () => {
    it('should retry failed steps according to configuration', async () => {
      // Mock component registry to fail first attempt
      let attemptCount = 0;
      mockComponentRegistry.findComponents = jest.fn().mockImplementation(async () => {
        attemptCount++;
        if (attemptCount === 1) {
          throw new Error('Simulated failure');
        }
        return ['test-component'];
      });
      
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      const options: IStepExecutionOptions = { retryAttempts: 2 };
      
      const results = await workflowExecutor.executeWorkflow(template, execution, options);
      
      expect(results).toHaveLength(1);
      expect(results[0].retryCount).toBeGreaterThan(0);
    });

    it('should handle component registry failures gracefully', async () => {
      mockComponentRegistry.findComponents = jest.fn().mockRejectedValue(new Error('Registry error'));
      
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(results[0].error).toBeDefined();
    });

    it('should execute dry run mode without actual operations', async () => {
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      const options: IStepExecutionOptions = { dryRun: true };
      
      const results = await workflowExecutor.executeWorkflow(template, execution, options);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toEqual(
        expect.objectContaining({ dryRun: true })
      );
    });

    it('should skip conditions when specified in options', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'conditional-step',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            condition: { type: 'component_exists', componentId: 'non-existent' },
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Conditional step'
          }
        ]
      });
      
      const execution = createTestExecution(template.id);
      const options: IStepExecutionOptions = { skipConditions: true };
      
      const results = await workflowExecutor.executeWorkflow(template, execution, options);
      
      expect(results).toHaveLength(1);
      expect(results[0].skipped).toBe(false); // Should execute despite condition
    });
  });

  /**
   * ============================================================================
   * SECTION 5: COMPONENT INTEGRATION & SIMULATION (Lines 321-380)
   * AI Context: "Component registry integration and step execution simulation"
   * ============================================================================
   */

  describe('Component Integration & Simulation', () => {
    it('should integrate with component registry for component discovery', async () => {
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      
      await workflowExecutor.executeWorkflow(template, execution);
      
      expect(mockComponentRegistry.findComponents).toHaveBeenCalled();
    });

    it('should simulate different operation types correctly', async () => {
      const template = createTestTemplate({
        operations: [
          {
            id: 'resource-cleanup',
            type: CleanupOperationType.RESOURCE_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-resources',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Resource cleanup'
          },
          {
            id: 'memory-cleanup',
            type: CleanupOperationType.MEMORY_CLEANUP,
            componentPattern: 'test-.*',
            operationName: 'cleanup-memory',
            parameters: {},
            timeout: 3000,
            retryPolicy: { maxRetries: 2, retryDelay: 500, backoffMultiplier: 2, maxRetryDelay: 5000, retryOnErrors: [] },
            dependsOn: [],
            priority: CleanupPriority.NORMAL,
            estimatedDuration: 500,
            description: 'Memory cleanup'
          }
        ]
      });
      
      const execution = createTestExecution(template.id);
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      expect(results).toHaveLength(2);
      expect(results[0].result).toEqual(
        expect.objectContaining({ operationType: 'resource-cleanup' })
      );
      expect(results[1].result).toEqual(
        expect.objectContaining({ operationType: 'memory-cleanup' })
      );
    });

    it('should handle component overrides in execution options', async () => {
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      const options: IStepExecutionOptions = {
        componentOverrides: { customParam: 'overrideValue' }
      };
      
      const results = await workflowExecutor.executeWorkflow(template, execution, options);
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
    });
  });

  /**
   * ============================================================================
   * SECTION 6: UTILITY FUNCTIONS & PERFORMANCE (Lines 381-400)
   * AI Context: "Utility functions, factory methods, and performance validation"
   * ============================================================================
   */

  describe('Utility Functions & Performance', () => {
    it('should create workflow executor with factory function', () => {
      const executor = createWorkflowExecutor(mockComponentRegistry);
      
      expect(executor).toBeInstanceOf(TemplateWorkflowExecutor);
      expect(executor.getConfig()).toEqual(
        expect.objectContaining(DEFAULT_WORKFLOW_CONFIG)
      );
    });

    it('should execute workflow using utility function', async () => {
      const template = createTestTemplate();
      const execution = createTestExecution(template.id);
      
      const results = await executeTemplateWorkflow(
        template,
        execution,
        mockComponentRegistry
      );
      
      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(true);
    });

    it('should maintain performance requirements for complex workflows', async () => {
      // LESSON LEARNED: Performance testing with multiple steps
      const startTime = performance.now();
      
      const template = createTestTemplate({
        operations: Array.from({ length: 10 }, (_, i) => ({
          id: `step-${i + 1}`,
          type: CleanupOperationType.RESOURCE_CLEANUP,
          componentPattern: 'test-.*',
          operationName: `cleanup-${i + 1}`,
          parameters: {},
          timeout: 1000,
          retryPolicy: { maxRetries: 1, retryDelay: 100, backoffMultiplier: 2, maxRetryDelay: 1000, retryOnErrors: [] },
          dependsOn: i > 0 ? [`step-${i}`] : [],
          priority: CleanupPriority.NORMAL,
          estimatedDuration: 100,
          description: `Step ${i + 1}`
        }))
      });
      
      const execution = createTestExecution(template.id);
      const results = await workflowExecutor.executeWorkflow(template, execution);
      
      const executionTime = performance.now() - startTime;
      expect(executionTime).toBeLessThan(100); // <100ms requirement
      
      expect(results).toHaveLength(10);
      expect(results.every(r => r.success)).toBe(true);
    });
  });
}); 