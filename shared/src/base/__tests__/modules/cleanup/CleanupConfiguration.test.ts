/**
 * CleanupConfiguration Test Suite
 * Tests configuration management and defaults
 */

import { 
  DEFAULT_ENHANCED_CLEANUP_CONFIG,
  validateConfig,
  mergeConfigs
} from '../../../modules/cleanup/CleanupConfiguration';

describe('CleanupConfiguration', () => {
  describe('DEFAULT_ENHANCED_CLEANUP_CONFIG', () => {
    it('should provide valid default configuration', () => {
      expect(DEFAULT_ENHANCED_CLEANUP_CONFIG).toBeDefined();
      expect(typeof DEFAULT_ENHANCED_CLEANUP_CONFIG).toBe('object');
    });

    it('should have required fields', () => {
      const config = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      
      expect(config.enabled).toBeDefined();
      expect(config.maxConcurrentOperations).toBeDefined();
      expect(config.operationTimeout).toBeDefined();
      expect(config.retryConfig).toBeDefined();
    });

    it('should have valid retry configuration', () => {
      const retryConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG.retryConfig;
      
      expect(retryConfig.maxRetries).toBeGreaterThanOrEqual(0);
      expect(retryConfig.retryDelay).toBeGreaterThan(0);
      expect(retryConfig.backoffMultiplier).toBeGreaterThan(0);
    });
  });

  describe('validateConfig', () => {
    it('should validate valid configuration', () => {
      const validConfig = {
        enabled: true,
        maxConcurrentOperations: 5,
        operationTimeout: 30000,
        retryConfig: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2.0,
          maxRetryDelay: 10000,
          retryOnErrors: ['TIMEOUT']
        }
      };

      const result = validateConfig(validConfig);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect invalid timeout values', () => {
      const invalidConfig = {
        enabled: true,
        maxConcurrentOperations: 5,
        operationTimeout: -1000,
        retryConfig: DEFAULT_ENHANCED_CLEANUP_CONFIG.retryConfig
      };

      const result = validateConfig(invalidConfig);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });

    it('should detect invalid retry configuration', () => {
      const invalidConfig = {
        enabled: true,
        maxConcurrentOperations: 5,
        operationTimeout: 30000,
        retryConfig: {
          maxRetries: -1,
          retryDelay: 1000,
          backoffMultiplier: 2.0,
          maxRetryDelay: 10000,
          retryOnErrors: ['TIMEOUT']
        }
      };

      const result = validateConfig(invalidConfig);
      
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('mergeConfigs', () => {
    it('should merge configurations correctly', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const overrides = {
        maxConcurrentOperations: 10,
        operationTimeout: 60000
      };

      const merged = mergeConfigs(baseConfig, overrides);
      
      expect(merged.maxConcurrentOperations).toBe(10);
      expect(merged.operationTimeout).toBe(60000);
      expect(merged.enabled).toBe(baseConfig.enabled);
    });

    it('should handle nested configuration merging', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const overrides = {
        retryConfig: {
          maxRetries: 5
        }
      };

      const merged = mergeConfigs(baseConfig, overrides);
      
      expect(merged.retryConfig.maxRetries).toBe(5);
      expect(merged.retryConfig.retryDelay).toBe(baseConfig.retryConfig.retryDelay);
    });

    it('should handle empty overrides', () => {
      const baseConfig = DEFAULT_ENHANCED_CLEANUP_CONFIG;
      const merged = mergeConfigs(baseConfig, {});
      
      expect(merged).toEqual(baseConfig);
    });
  });
}); 