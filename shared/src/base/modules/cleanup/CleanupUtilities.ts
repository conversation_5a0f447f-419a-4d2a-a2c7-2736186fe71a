/**
 * @file Cleanup Utilities - Helper Functions & Validation
 * @filepath shared/src/base/modules/cleanup/CleanupUtilities.ts
 * @task-id M-TSK-01.SUB-01.REF-01.UTILITIES
 * @component cleanup-utilities
 * @created 2025-07-24 16:32:57 +03
 * 
 * @description
 * Utility functions and validation helpers for enhanced cleanup operations
 * with Jest compatibility and performance optimization.
 * 
 * LESSONS LEARNED INTEGRATION:
 * - Jest compatibility: Safe utility functions without timing dependencies
 * - Memory safety: Efficient helper functions with proper cleanup
 * - Performance: Optimized algorithms for common operations
 * - Testing: Mock-friendly utility patterns
 */

import { CleanupPriority, CleanupOperationType } from '../../CleanupCoordinator';
import {
  ICleanupTemplate,
  ICleanupTemplateStep,
  IValidationResult,
  IValidationIssue,
  IStepCondition,
  IStepExecutionContext,
  ITemplateExecutionContext,
  IOptimizationOpportunity,
  IRiskFactor,
  IEnhancedCleanupConfig
} from '../../types/CleanupTypes';
import { ICleanupOperation } from '../../CleanupCoordinator';
import { DEFAULT_TEMPLATE_CONSTANTS } from './CleanupConfiguration';

// ============================================================================
// TEMPLATE VALIDATION UTILITIES
// ============================================================================

/**
 * Validate cleanup template structure and dependencies
 * LESSON LEARNED: Comprehensive validation without external dependencies
 */
export function validateTemplate(template: ICleanupTemplate): IValidationResult {
  const issues: IValidationIssue[] = [];
  const warnings: string[] = [];
  const suggestions: string[] = [];

  // Basic structure validation
  if (!template.id || template.id.length < DEFAULT_TEMPLATE_CONSTANTS.MIN_TEMPLATE_ID_LENGTH) {
    issues.push({
      type: 'invalid_id',
      message: `Template ID must be at least ${DEFAULT_TEMPLATE_CONSTANTS.MIN_TEMPLATE_ID_LENGTH} characters`,
      severity: 'error'
    });
  }

  if (!template.name || template.name.length > DEFAULT_TEMPLATE_CONSTANTS.MAX_TEMPLATE_NAME_LENGTH) {
    issues.push({
      type: 'invalid_name',
      message: `Template name must be provided and ≤${DEFAULT_TEMPLATE_CONSTANTS.MAX_TEMPLATE_NAME_LENGTH} characters`,
      severity: 'error'
    });
  }

  if (!template.operations || template.operations.length === 0) {
    issues.push({
      type: 'no_operations',
      message: 'Template must contain at least one operation',
      severity: 'error'
    });
  }

  // Operation validation
  if (template.operations) {
    const stepIds = new Set<string>();
    
    template.operations.forEach((step, index) => {
      // Check for duplicate step IDs
      if (stepIds.has(step.id)) {
        issues.push({
          type: 'duplicate_step_id',
          message: `Duplicate step ID: ${step.id}`,
          severity: 'error',
          stepId: step.id
        });
      }
      stepIds.add(step.id);

      // Validate step dependencies
      if (step.dependsOn) {
        step.dependsOn.forEach(depId => {
          if (!stepIds.has(depId) && !template.operations.some(op => op.id === depId)) {
            warnings.push(`Step ${step.id} depends on unknown step: ${depId}`);
          }
        });
      }

      // Validate component pattern
      try {
        new RegExp(step.componentPattern);
      } catch (error) {
        issues.push({
          type: 'invalid_regex',
          message: `Invalid component pattern regex in step ${step.id}: ${step.componentPattern}`,
          severity: 'error',
          stepId: step.id,
          field: 'componentPattern'
        });
      }

      // Performance suggestions
      if (step.timeout > 300000) { // 5 minutes
        suggestions.push(`Step ${step.id} has a very long timeout (${step.timeout}ms). Consider breaking it into smaller steps.`);
      }
    });
  }

  // Validate rollback steps
  if (template.rollbackSteps && template.rollbackSteps.length > 0) {
    template.rollbackSteps.forEach(step => {
      if (!step.rollbackOperation) {
        warnings.push(`Rollback step ${step.id} should specify a rollback operation`);
      }
    });
  }

  return {
    valid: issues.filter(i => i.severity === 'error').length === 0,
    issues,
    warnings,
    suggestions
  };
}

/**
 * Validate step condition
 * LESSON LEARNED: Safe condition evaluation without external calls
 */
export function evaluateStepCondition(condition: IStepCondition, context: IStepExecutionContext): boolean {
  try {
    switch (condition.type) {
      case 'always':
        return true;
      case 'on_success':
        return context.previousResults.size === 0 || 
               Array.from(context.previousResults.values()).every(result => result.success);
      case 'on_failure':
        return Array.from(context.previousResults.values()).some(result => !result.success);
      case 'component_exists':
        return condition.componentId ? 
               context.globalContext.targetComponents.includes(condition.componentId) : true;
      case 'resource_available':
        // Simplified resource availability check
        return condition.resourceThreshold ? 
               (process.memoryUsage().heapUsed || 0) < (condition.resourceThreshold * 1024 * 1024) : true;
      case 'custom':
        return condition.customCondition ? condition.customCondition(context) : true;
      default:
        return true;
    }
  } catch (error) {
    // Return false for any evaluation errors
    return false;
  }
}

// ============================================================================
// EXECUTION UTILITIES
// ============================================================================

/**
 * Generate unique execution ID
 */
export function generateExecutionId(templateId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `template-exec-${templateId}-${timestamp}-${random}`;
}

/**
 * Generate unique checkpoint ID
 */
export function generateCheckpointId(operationId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `checkpoint-${operationId}-${timestamp}-${random}`;
}

/**
 * Find components matching a pattern
 * LESSON LEARNED: Safe regex matching with fallback
 */
export function findMatchingComponents(pattern: string, components: string[]): string[] {
  try {
    const regex = new RegExp(pattern, 'i');
    return components.filter(component => regex.test(component));
  } catch (error) {
    // Fallback to simple string matching if regex fails
    return components.filter(component => 
      component.toLowerCase().includes(pattern.toLowerCase())
    );
  }
}

/**
 * Estimate operation duration based on type and priority
 */
export function estimateOperationDuration(operation: ICleanupOperation | undefined): number {
  if (!operation) return 1000; // Default 1 second
  
  // Base estimation on operation type
  let baseTime = 1000;
  if (operation.type.toString().includes('CLEANUP')) baseTime = 2000;
  else if (operation.type.toString().includes('AUDIT')) baseTime = 500;
  else if (operation.type.toString().includes('OPTIMIZATION')) baseTime = 3000;
  
  // Apply priority multiplier
  const priorityMultiplier = operation.priority === CleanupPriority.EMERGENCY ? 0.5 : 1.0;
  
  return Math.max(100, baseTime * priorityMultiplier);
}

/**
 * Sort operations by dependency order
 * LESSON LEARNED: Optimized sorting to prevent infinite loops
 */
export function sortOperationsByDependencies(operations: ICleanupOperation[]): ICleanupOperation[] {
  const sorted: ICleanupOperation[] = [];
  const visited = new Set<string>();
  const visiting = new Set<string>();

  const visit = (operation: ICleanupOperation): void => {
    if (visiting.has(operation.id)) {
      // Circular dependency detected - skip this operation
      return;
    }
    
    if (visited.has(operation.id)) {
      return;
    }

    visiting.add(operation.id);

    // Visit dependencies first
    if (operation.dependencies) {
      operation.dependencies.forEach(depId => {
        const depOp = operations.find(op => op.id === depId);
        if (depOp) {
          visit(depOp);
        }
      });
    }

    visiting.delete(operation.id);
    visited.add(operation.id);
    sorted.push(operation);
  };

  operations.forEach(operation => {
    if (!visited.has(operation.id)) {
      visit(operation);
    }
  });

  return sorted;
}

// ============================================================================
// DEPENDENCY ANALYSIS UTILITIES
// ============================================================================

/**
 * Generate dependency analysis cache key
 */
export function generateDependencyCacheKey(operations: ICleanupOperation[]): string {
  const operationSignature = operations
    .map(op => `${op.id}:${(op.dependencies || []).sort().join(',')}`)
    .sort()
    .join('|');
  
  // Simple hash for cache key
  let hash = 0;
  for (let i = 0; i < operationSignature.length; i++) {
    const char = operationSignature.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return `dep-analysis-${Math.abs(hash)}`;
}

/**
 * Identify optimization opportunities
 */
export function identifyOptimizationOpportunities(
  operations: ICleanupOperation[],
  parallelGroups: string[][]
): IOptimizationOpportunity[] {
  const opportunities: IOptimizationOpportunity[] = [];

  // Look for parallelization opportunities
  for (const group of parallelGroups) {
    if (group.length > 1) {
      opportunities.push({
        type: 'parallelization',
        description: `Parallel execution of ${group.length} operations`,
        estimatedImprovement: Math.min(50, group.length * 10), // Up to 50% improvement
        implementationComplexity: 'medium',
        riskLevel: 'low',
        affectedOperations: group
      });
    }
  }

  // Look for priority adjustment opportunities
  const highPriorityOps = operations.filter(op => op.priority === CleanupPriority.EMERGENCY);
  const lowPriorityOps = operations.filter(op => op.priority === CleanupPriority.LOW);
  
  if (highPriorityOps.length > lowPriorityOps.length * 2) {
    opportunities.push({
      type: 'priority_adjustment',
      description: 'Consider rebalancing operation priorities for better performance',
      estimatedImprovement: 15,
      implementationComplexity: 'low',
      riskLevel: 'low',
      affectedOperations: highPriorityOps.map(op => op.id)
    });
  }

  return opportunities;
}

/**
 * Generate mitigation strategies for risk factors
 */
export function generateMitigationStrategies(riskFactors: IRiskFactor[]): string[] {
  const strategies: string[] = [];
  
  riskFactors.forEach(factor => {
    switch (factor.type) {
      case 'circular_dependency':
        strategies.push('Review and refactor operation dependencies to eliminate cycles');
        break;
      case 'resource_contention':
        strategies.push('Consider implementing resource pooling or queue management');
        break;
      case 'timing_constraint':
        strategies.push('Optimize operation timeouts and implement parallel execution');
        break;
      case 'external_dependency':
        strategies.push('Implement fallback mechanisms and health check monitoring');
        break;
    }
  });

  // Remove duplicates using ES6+ Set and spread operator
  return Array.from(new Set(strategies));
}

/**
 * Generate contingency plans for risk factors
 */
export function generateContingencyPlans(riskFactors: IRiskFactor[]): string[] {
  const plans: string[] = [];
  
  if (riskFactors.some(f => f.severity === 'critical')) {
    plans.push('Emergency rollback procedures should be prepared');
    plans.push('Manual intervention procedures should be documented');
  }
  
  if (riskFactors.some(f => f.type === 'resource_contention')) {
    plans.push('Alternative resource allocation strategies should be available');
  }

  if (riskFactors.some(f => f.type === 'timing_constraint')) {
    plans.push('Timeout extension protocols should be established');
  }

  // Default contingency plans
  plans.push('Comprehensive logging and monitoring for failure analysis');
  plans.push('Automated alert system for dependency analysis anomalies');

  return Array.from(new Set(plans));
}

// ============================================================================
// PERFORMANCE UTILITIES
// ============================================================================

/**
 * Calculate simple checksum for data integrity
 */
export function calculateChecksum(data: any): string {
  const jsonString = JSON.stringify(data);
  let hash = 0;
  
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

/**
 * Deep clone an object safely
 * LESSON LEARNED: Safe cloning with error handling
 */
export function deepClone<T>(obj: T): T {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (error) {
    // Fallback to shallow copy for objects that can't be JSON serialized
    if (typeof obj === 'object' && obj !== null) {
      return { ...obj } as T;
    }
    return obj;
  }
}

/**
 * Safely get nested property from object
 */
export function getNestedProperty(obj: any, path: string, defaultValue: any = undefined): any {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
  } catch (error) {
    return defaultValue;
  }
}

/**
 * Format duration in human-readable format
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * Sanitize string for safe logging
 */
export function sanitizeForLogging(value: any): string {
  if (typeof value === 'string') {
    // Remove potential sensitive patterns and limit length
    return value
      .replace(/password[=:]\s*\S+/gi, 'password=***')
      .replace(/token[=:]\s*\S+/gi, 'token=***')
      .replace(/key[=:]\s*\S+/gi, 'key=***')
      .substring(0, 1000);
  }
  
  if (typeof value === 'object' && value !== null) {
    try {
      const sanitized = { ...value };
      
      // Remove sensitive fields
      ['password', 'token', 'key', 'secret', 'auth'].forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '***';
        }
      });
      
      return JSON.stringify(sanitized, null, 2).substring(0, 1000);
    } catch (error) {
      return '[Object - Unable to serialize]';
    }
  }
  
  return String(value).substring(0, 1000);
}

// ============================================================================
// TEMPLATE UTILITIES
// ============================================================================

/**
 * Create default template step
 */
export function createDefaultTemplateStep(
  id: string,
  operationName: string,
  componentPattern: string = '.*'
): ICleanupTemplateStep {
  return {
    id,
    type: CleanupOperationType.RESOURCE_CLEANUP,
    componentPattern,
    operationName,
    parameters: {},
    timeout: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_STEP_TIMEOUT,
    retryPolicy: {
      maxRetries: 3,
      retryDelay: 1000,
      backoffMultiplier: 2.0,
      maxRetryDelay: 10000,
      retryOnErrors: ['TimeoutError', 'ResourceBusyError']
    },
    dependsOn: [],
    priority: DEFAULT_TEMPLATE_CONSTANTS.DEFAULT_PRIORITY,
    estimatedDuration: 5000,
    description: `Execute ${operationName} on matching components`
  };
}

/**
 * Merge template execution contexts
 */
export function mergeExecutionContexts(
  base: ITemplateExecutionContext,
  updates: Partial<ITemplateExecutionContext>
): ITemplateExecutionContext {
  return {
    ...base,
    ...updates,
    parameters: { ...base.parameters, ...updates.parameters },
    systemState: { ...base.systemState, ...updates.systemState }
  };
}

/**
 * Validate configuration completeness
 */
export function validateConfigurationCompleteness(config: Partial<IEnhancedCleanupConfig>): string[] {
  const issues: string[] = [];
  
  if (config.rollbackEnabled && !config.maxCheckpoints) {
    issues.push('maxCheckpoints should be specified when rollback is enabled');
  }
  
  if (config.performanceMonitoringEnabled && !config.cleanupIntervalMs) {
    issues.push('cleanupIntervalMs should be specified when performance monitoring is enabled');
  }
  
  if (config.templateValidationEnabled === undefined) {
    issues.push('templateValidationEnabled should be explicitly set');
  }
  
  return issues;
}

// ============================================================================
// EXPORT UTILITY COLLECTIONS
// ============================================================================

/**
 * Collection of validation utilities
 */
export const ValidationUtils = {
  validateTemplate,
  evaluateStepCondition,
  validateConfigurationCompleteness
};

/**
 * Collection of execution utilities  
 */
export const ExecutionUtils = {
  generateExecutionId,
  generateCheckpointId,
  findMatchingComponents,
  estimateOperationDuration,
  sortOperationsByDependencies
};

/**
 * Collection of performance utilities
 */
export const PerformanceUtils = {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging
};

/**
 * Collection of template utilities
 */
export const TemplateUtils = {
  createDefaultTemplateStep,
  mergeExecutionContexts
};

/**
 * Collection of analysis utilities
 */
export const AnalysisUtils = {
  generateDependencyCacheKey,
  identifyOptimizationOpportunities,
  generateMitigationStrategies,
  generateContingencyPlans
}; 