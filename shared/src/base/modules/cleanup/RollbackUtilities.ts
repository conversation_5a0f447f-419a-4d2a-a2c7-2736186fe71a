/**
 * @file Rollback Utilities - Helper Functions and Assessment Logic
 * @filepath shared/src/base/modules/cleanup/RollbackUtilities.ts
 * @task-id M-TSK-01.SUB-01.REF-01.ROLLBACK.UTILITIES
 * @component rollback-utilities
 * @created 2025-07-25 14:30:00 +03
 * 
 * @description
 * Extracted utility functions for rollback management operations.
 * Implements assessment, validation, and helper functions with Jest compatibility.
 * 
 * EXTRACTION RATIONALE:
 * - Domain separation: Utility functions isolated from core rollback logic
 * - Reusability: Helper functions can be used across rollback components
 * - Testability: Independent utility unit testing
 * - Maintainability: Clear separation of concerns
 */

import {
  ICheckpoint,
  IRollbackAction,
  ISystemSnapshot
} from '../../types/CleanupTypes';

// ============================================================================
// ID GENERATION UTILITIES
// ============================================================================

/**
 * Generate unique checkpoint ID
 */
export function generateCheckpointId(operationId: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 8);
  return `checkpoint-${operationId}-${timestamp}-${random}`;
}

// ============================================================================
// DATA MANIPULATION UTILITIES
// ============================================================================

/**
 * Deep clone an object safely
 */
export function deepClone<T>(obj: T): T {
  return JSON.parse(JSON.stringify(obj));
}

/**
 * Calculate checkpoint checksum
 */
export async function calculateCheckpointChecksum(
  state: any, 
  actions: IRollbackAction[], 
  snapshot: ISystemSnapshot
): Promise<string> {
  const data = JSON.stringify({ 
    state, 
    actionsCount: actions.length, 
    snapshotTime: snapshot.timestamp 
  });
  return Buffer.from(data).toString('base64').substring(0, 16);
}

// ============================================================================
// ROLLBACK ACTION UTILITIES
// ============================================================================

/**
 * Sort rollback actions by priority and duration
 * LESSON LEARNED: Optimized sorting to prevent hanging operations
 */
export function sortRollbackActions(actions: IRollbackAction[]): IRollbackAction[] {
  return [...actions].sort((a, b) => {
    // Primary sort: priority (higher priority first)
    const priorityDiff = b.priority - a.priority;
    if (priorityDiff !== 0) return priorityDiff;
    
    // Secondary sort: estimated duration (longer operations first for early failure detection)
    return b.estimatedDuration - a.estimatedDuration;
  });
}

// ============================================================================
// ASSESSMENT UTILITIES
// ============================================================================

/**
 * Assess rollback complexity based on checkpoint characteristics
 */
export function assessRollbackComplexity(checkpoint: ICheckpoint): 'simple' | 'moderate' | 'complex' {
  const actionCount = checkpoint.rollbackActions.length;
  if (actionCount <= 3) return 'simple';
  if (actionCount <= 10) return 'moderate';
  return 'complex';
}

/**
 * Estimate rollback execution time
 */
export function estimateRollbackTime(checkpoint: ICheckpoint): number {
  return checkpoint.rollbackActions.reduce((total, action) => total + action.estimatedDuration, 0);
}

/**
 * Assess rollback risk level
 */
export function assessRollbackRisk(checkpoint: ICheckpoint): 'low' | 'medium' | 'high' {
  const criticalActions = checkpoint.rollbackActions.filter(a => a.critical).length;
  const ageHours = (Date.now() - checkpoint.timestamp.getTime()) / (1000 * 60 * 60);
  
  if (criticalActions === 0 && ageHours < 1) return 'low';
  if (criticalActions <= 2 && ageHours < 24) return 'medium';
  return 'high';
}

/**
 * Identify rollback limitations
 */
export function identifyRollbackLimitations(checkpoint: ICheckpoint): string[] {
  const limitations: string[] = [];
  
  const ageHours = (Date.now() - checkpoint.timestamp.getTime()) / (1000 * 60 * 60);
  if (ageHours > 24) {
    limitations.push('Checkpoint is older than 24 hours - may have stale state');
  }

  const criticalActions = checkpoint.rollbackActions.filter(a => a.critical).length;
  if (criticalActions > 5) {
    limitations.push('High number of critical actions - increased failure risk');
  }

  return limitations;
}

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

/**
 * Validate checkpoint integrity
 */
export function validateCheckpointIntegrity(checkpoint: ICheckpoint): boolean {
  if (!checkpoint.id || !checkpoint.operationId) return false;
  if (!checkpoint.timestamp || checkpoint.timestamp > new Date()) return false;
  if (!Array.isArray(checkpoint.rollbackActions)) return false;
  
  return true;
}

/**
 * Validate rollback action
 */
export function validateRollbackAction(action: IRollbackAction): boolean {
  if (!action.type || !action.parameters) return false;
  if (action.priority < 0 || action.priority > 10) return false;
  if (action.estimatedDuration < 0) return false;
  
  return true;
}

// ============================================================================
// UTILITY COLLECTION
// ============================================================================

/**
 * Collection of rollback utilities
 */
export const RollbackUtils = {
  generateCheckpointId,
  deepClone,
  calculateCheckpointChecksum,
  sortRollbackActions,
  assessRollbackComplexity,
  estimateRollbackTime,
  assessRollbackRisk,
  identifyRollbackLimitations,
  validateCheckpointIntegrity,
  validateRollbackAction
}; 