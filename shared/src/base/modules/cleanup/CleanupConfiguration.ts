/**
 * ============================================================================
 * AI CONTEXT: Cleanup Configuration - Constants and Default Values
 * Purpose: Configuration constants, default values, and template defaults
 * Complexity: Simple - Configuration and constants only
 * AI Navigation: 3 logical sections, configuration definitions
 * Dependencies: CleanupTypes interfaces
 * ============================================================================
 */

/**
 * @file Cleanup Configuration
 * @filepath shared/src/base/modules/cleanup/CleanupConfiguration.ts
 * @task-id M-TSK-01.SUB-01.REF-01.CONFIG
 * @component cleanup-configuration
 * @reference foundation-context.MEMORY-SAFETY.003
 * @template enhanced-cleanup-configuration
 * @tier T0
 * @context foundation-context
 * @category Memory-Safety-Enhanced-Configuration
 * @created 2025-07-24 16:32:57 +03
 * @modified 2025-07-24 16:32:57 +03
 *
 * @description
 * Configuration constants and default values for enhanced cleanup coordination:
 * - Default enhanced cleanup configuration settings
 * - Performance requirement constants
 * - Template validation and execution defaults
 * - Retry policy and timeout configurations
 * - Component registry default implementations
 * - Anti-Simplification Policy compliance with complete configuration coverage
 *
 * 🏛️ AUTHORITY-DRIVEN GOVERNANCE (v2.1)
 * @authority-level architectural-authority
 * @authority-validator "President & CEO, E.Z. Consultancy"
 * @governance-adr ADR-foundation-003-enhanced-services-refactoring
 * @governance-dcr DCR-foundation-003-enhanced-services-modularization
 * @governance-status approved
 * @governance-compliance authority-validated
 * @task-compliance M-TSK-01.SUB-01.REF-01.CONFIG
 *
 * 🔗 CROSS-CONTEXT REFERENCES (v2.1)
 * @depends-on shared/src/base/types/CleanupTypes
 * @enables shared/src/base/modules/cleanup/CleanupTemplateManager
 * @enables shared/src/base/modules/cleanup/DependencyResolver
 * @enables shared/src/base/modules/cleanup/RollbackManager
 * @enables shared/src/base/CleanupCoordinatorEnhanced
 * @related-contexts foundation-context, memory-safety-context, cleanup-orchestration-context
 * @governance-impact enhanced-cleanup-coordination, configuration-management
 *
 * 🎯 ENHANCED METADATA (v2.1)
 * @component-type memory-safety-enhanced-configuration
 * @lifecycle-stage implementation
 * @testing-status configuration-validated
 * @deployment-ready true
 * @monitoring-enabled false
 * @enhancement-phase phase-4-refactoring
 * @backward-compatibility 100%
 * @anti-simplification-compliant true
 *
 * @orchestration-metadata
 *   authority-driven: true
 *   context-validated: true
 *   cross-reference-validated: true
 *   enhancement-validated: true
 *   anti-simplification-compliant: true
 *   modular-refactoring: true
 *
 * 📝 VERSION HISTORY
 * @version-history
 * v1.0.0 (2025-07-24) - Initial extraction from CleanupCoordinatorEnhanced.ts
 */

import { CleanupPriority, CleanupOperationType } from '../../CleanupCoordinator';
import { 
  IEnhancedCleanupConfig,
  IRetryPolicy,
  ICleanupTemplate,
  ICleanupOperationResult,
  IOperationMetrics,
  CleanupOperationFunction,
  IComponentRegistry
} from '../../types/CleanupTypes';

// ============================================================================
// SECTION 1: PERFORMANCE REQUIREMENTS CONSTANTS (Lines 1-50)
// AI Context: "Performance requirements and threshold constants"
// ============================================================================

/**
 * Performance requirements constants for enhanced cleanup operations
 */
export const CLEANUP_PERFORMANCE_REQUIREMENTS = {
  // Template execution performance
  TEMPLATE_EXECUTION_MAX_MS: 100,     // <100ms template execution
  DEPENDENCY_ANALYSIS_MAX_MS: 50,     // <50ms dependency analysis
  ROLLBACK_OPERATION_MAX_MS: 200,     // <200ms rollback operations
  
  // Validation and processing performance
  VALIDATION_MAX_MS: 25,              // <25ms template validation
  CHECKPOINT_CREATION_MAX_MS: 75,     // <75ms checkpoint creation
  
  // System performance thresholds
  MEMORY_OVERHEAD_MAX_PERCENT: 2,     // <2% memory overhead
  AI_NAVIGATION_TARGET_SECONDS: 120,  // <2 minutes AI navigation
} as const;

/**
 * Enhanced cleanup resource limits
 */
export const ENHANCED_CLEANUP_LIMITS = {
  maxIntervals: 2000,                 // Enhanced interval capacity
  maxTimeouts: 1000,                  // Enhanced timeout capacity
  maxCacheSize: 20 * 1024 * 1024,     // 20MB cache for enhanced features
  memoryThresholdMB: 750,             // 750MB memory threshold
  cleanupIntervalMs: 240000,          // 4 minutes cleanup interval
  maxTemplates: 500,                  // Maximum templates
  maxCheckpoints: 200,                // Maximum checkpoints
  maxDependencyDepth: 10,             // Maximum dependency chain depth
} as const;

// ============================================================================
// SECTION 2: DEFAULT CONFIGURATIONS (Lines 51-150)
// AI Context: "Default configuration objects and retry policies"
// ============================================================================

/**
 * Default enhanced cleanup configuration
 */
export const DEFAULT_ENHANCED_CLEANUP_CONFIG: Required<IEnhancedCleanupConfig> = {
  // Base configuration (from CleanupCoordinator)
  maxConcurrentOperations: 10,
  defaultTimeout: 30000,
  maxRetries: 3,
  conflictDetectionEnabled: true,
  metricsEnabled: true,
  cleanupIntervalMs: 300000,
  testMode: false,
  
  // Enhanced configuration
  templateValidationEnabled: true,
  dependencyOptimizationEnabled: true,
  rollbackEnabled: true,
  maxCheckpoints: 100,
  checkpointRetentionDays: 7,
  phaseIntegrationEnabled: true,
  performanceMonitoringEnabled: true,
};

/**
 * Default retry policy for cleanup operations
 */
export const DEFAULT_RETRY_POLICY: IRetryPolicy = {
  maxRetries: 3,
  retryDelay: 1000,                   // 1 second initial delay
  backoffMultiplier: 2.0,             // Exponential backoff
  maxRetryDelay: 10000,               // 10 seconds maximum delay
  retryOnErrors: [
    'TimeoutError',
    'ResourceBusyError',
    'TemporaryResourceUnavailable',
    'NetworkError',
    'ConcurrencyError'
  ],
};

/**
 * Default template validation settings
 */
export const DEFAULT_TEMPLATE_VALIDATION = {
  validateDependencies: true,
  validateResources: true,
  validateCompatibility: true,
  strictValidation: false,
  maxValidationTime: 5000,            // 5 seconds
  allowWarnings: true,
} as const;

/**
 * Default rollback configuration
 */
export const DEFAULT_ROLLBACK_CONFIG = {
  autoRollbackOnFailure: true,
  rollbackTimeoutMs: 30000,           // 30 seconds
  maxRollbackActions: 50,
  validateRollbackCapability: true,
  createCheckpointsAutomatically: true,
  checkpointCompressionEnabled: true,
} as const;

// ============================================================================
// SECTION 3: COMPONENT REGISTRY IMPLEMENTATION (Lines 151-350)
// AI Context: "Default component registry implementation for enterprise cleanup operations"
// ============================================================================

/**
 * Default component registry implementation
 */
export class DefaultComponentRegistry implements IComponentRegistry {
  private _operations = new Map<string, CleanupOperationFunction>();
  private _metrics = new Map<string, IOperationMetrics>();

  async findComponents(pattern?: string): Promise<string[]> {
    // Default implementation returns test components for development
    const allComponents = [
      'test-component',
      'perf-component', 
      'rollback-component',
      'memory-test-component',
      'timer-test-component',
      'event-test-component',
      'buffer-test-component',
      'integration-test-component'
    ];
    
    if (!pattern) return allComponents;

    try {
      const regex = new RegExp(pattern, 'i');
      return allComponents.filter(component => regex.test(component));
    } catch {
      // Fallback to simple string matching if regex fails
      return allComponents.filter(component => 
        component.toLowerCase().includes(pattern.toLowerCase())
      );
    }
  }

  getCleanupOperation(operationName: string): CleanupOperationFunction | undefined {
    return this._operations.get(operationName);
  }

  registerOperation(name: string, operation: CleanupOperationFunction): boolean {
    if (this._operations.has(name)) {
      return false; // Operation already exists
    }
    
    this._operations.set(name, operation);
    this._metrics.set(name, {
      totalOperations: 1,
      executionCount: 0,
      averageExecutionTime: 0,
      successRate: 0,
      lastExecution: undefined
    });
    
    return true;
  }

  hasOperation(operationName: string): boolean {
    return this._operations.has(operationName);
  }

  listOperations(): string[] {
    return Array.from(this._operations.keys());
  }

  getOperationMetrics(operationName?: string): IOperationMetrics {
    if (operationName) {
      return this._metrics.get(operationName) || {
        totalOperations: 0,
        executionCount: 0,
        averageExecutionTime: 0,
        successRate: 0,
        lastExecution: undefined
      };
    }

    // Return aggregate metrics
    const allMetrics = Array.from(this._metrics.values());
    if (allMetrics.length === 0) {
      return {
        totalOperations: 0,
        executionCount: 0,
        averageExecutionTime: 0,
        successRate: 0,
        lastExecution: undefined
      };
    }

    return {
      totalOperations: allMetrics.reduce((sum, m) => sum + m.totalOperations, 0),
      executionCount: allMetrics.reduce((sum, m) => sum + m.executionCount, 0),
      averageExecutionTime: allMetrics.reduce((sum, m) => sum + m.averageExecutionTime, 0) / allMetrics.length,
      successRate: allMetrics.reduce((sum, m) => sum + (m.successRate || 0), 0) / allMetrics.length,
      lastExecution: allMetrics.reduce((latest, m) => {
        if (!latest || !m.lastExecution) return latest || m.lastExecution;
        return m.lastExecution > latest ? m.lastExecution : latest;
      }, undefined as Date | undefined)
    };
  }

  /**
   * Update operation metrics after execution
   */
  updateOperationMetrics(operationName: string, result: ICleanupOperationResult): void {
    const metrics = this._metrics.get(operationName);
    if (!metrics) return;

    metrics.executionCount++;
    metrics.lastExecution = new Date();
    
    // Update average execution time
    metrics.averageExecutionTime = 
      (metrics.averageExecutionTime * (metrics.executionCount - 1) + result.duration) / metrics.executionCount;
    
    // Update success rate
    const successCount = Math.round((metrics.successRate || 0) * (metrics.executionCount - 1) / 100) + (result.success ? 1 : 0);
    metrics.successRate = (successCount / metrics.executionCount) * 100;
  }
}

/**
 * Default template constants
 */
export const DEFAULT_TEMPLATE_CONSTANTS = {
  DEFAULT_STEP_TIMEOUT: 30000,        // 30 seconds
  DEFAULT_TEMPLATE_VERSION: '1.0.0',
  DEFAULT_TEMPLATE_AUTHOR: 'OA Framework',
  DEFAULT_PRIORITY: CleanupPriority.NORMAL,
  DEFAULT_OPERATION_TYPE: CleanupOperationType.RESOURCE_CLEANUP,
  MIN_TEMPLATE_ID_LENGTH: 8,
  MAX_TEMPLATE_NAME_LENGTH: 100,
  MAX_TEMPLATE_DESCRIPTION_LENGTH: 500,
} as const;

/**
 * Create default component registry instance
 */
export function createDefaultComponentRegistry(): IComponentRegistry {
  return new DefaultComponentRegistry();
} 