/**
 * @file Utility Performance - Performance and Data Utilities
 * @filepath shared/src/base/modules/cleanup/UtilityPerformance.ts
 * @task-id M-TSK-01.SUB-01.REF-01.UTILITIES.PERFORMANCE
 * @component utility-performance
 * @created 2025-07-25 14:15:00 +03
 * 
 * @description
 * Extracted performance utilities for enhanced cleanup operations.
 * Implements data integrity, cloning, formatting, and sanitization with Jest compatibility.
 * 
 * EXTRACTION RATIONALE:
 * - Domain separation: Performance utilities isolated from business logic
 * - Memory safety: Efficient data handling and cloning functions
 * - Testability: Independent performance unit testing
 * - Reusability: Performance functions used across multiple components
 */

// ============================================================================
// DATA INTEGRITY UTILITIES
// ============================================================================

/**
 * Calculate simple checksum for data integrity
 */
export function calculateChecksum(data: any): string {
  const jsonString = JSON.stringify(data);
  let hash = 0;
  
  for (let i = 0; i < jsonString.length; i++) {
    const char = jsonString.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
}

// ============================================================================
// DATA MANIPULATION UTILITIES
// ============================================================================

/**
 * Deep clone an object safely
 * LESSON LEARNED: Safe cloning with error handling
 */
export function deepClone<T>(obj: T): T {
  try {
    return JSON.parse(JSON.stringify(obj));
  } catch (error) {
    // Fallback to shallow copy for objects that can't be JSON serialized
    if (typeof obj === 'object' && obj !== null) {
      return { ...obj } as T;
    }
    return obj;
  }
}

/**
 * Safely get nested property from object
 */
export function getNestedProperty(obj: any, path: string, defaultValue: any = undefined): any {
  try {
    return path.split('.').reduce((current, key) => current?.[key], obj) ?? defaultValue;
  } catch (error) {
    return defaultValue;
  }
}

// ============================================================================
// FORMATTING UTILITIES
// ============================================================================

/**
 * Format duration in human-readable format
 */
export function formatDuration(milliseconds: number): string {
  if (milliseconds < 1000) {
    return `${milliseconds}ms`;
  }
  
  const seconds = Math.floor(milliseconds / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

// ============================================================================
// SECURITY UTILITIES
// ============================================================================

/**
 * Sanitize string for safe logging
 */
export function sanitizeForLogging(value: any): string {
  if (typeof value === 'string') {
    // Remove potential sensitive patterns and limit length
    return value
      .replace(/password[=:]\s*\S+/gi, 'password=***')
      .replace(/token[=:]\s*\S+/gi, 'token=***')
      .replace(/key[=:]\s*\S+/gi, 'key=***')
      .substring(0, 1000);
  }
  
  if (typeof value === 'object' && value !== null) {
    try {
      const sanitized = { ...value };
      
      // Remove sensitive fields
      ['password', 'token', 'key', 'secret', 'auth'].forEach(field => {
        if (sanitized[field]) {
          sanitized[field] = '***';
        }
      });
      
      return JSON.stringify(sanitized, null, 2).substring(0, 1000);
    } catch (error) {
      return '[Object - Unable to serialize]';
    }
  }
  
  return String(value).substring(0, 1000);
}

// ============================================================================
// PERFORMANCE UTILITY COLLECTION
// ============================================================================

/**
 * Collection of performance utilities
 */
export const PerformanceUtils = {
  calculateChecksum,
  deepClone,
  getNestedProperty,
  formatDuration,
  sanitizeForLogging
}; 