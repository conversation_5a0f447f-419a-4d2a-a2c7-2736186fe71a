npm test -- --testPathPattern="MemorySafetyManagerEnhanced.test.ts" --verbose

> oa-framework@1.0.0 test
> jest --testPathPattern=MemorySafetyManagerEnhanced.test.ts --verbose

  console.log
    [JEST SETUP] Global timer functions mocked - NO REAL TIMERS CAN BE CREATED

      at Object.<anonymous> (jest.setup.js:19:9)

  console.log
    [JEST SETUP] Module mocking delegated to individual test files

      at Object.<anonymous> (jest.setup.js:23:9)

 FAIL  shared/src/base/__tests__/MemorySafetyManagerEnhanced.test.ts (396 MB heap size)
  MemorySafetyManagerEnhanced
    Component Discovery
      ✓ should discover and register memory-safe components (13 ms)
      ✓ should validate component compatibility with comprehensive checks (7 ms)
      ✕ should auto-integrate compatible components (7 ms)
      ✓ should handle component discovery performance requirements (6 ms)
    System Coordination
      ✓ should create and coordinate component groups (7 ms)
      ✓ should execute component chains sequentially (12 ms)
      ✓ should create resource sharing groups (5 ms)
      ✓ should handle group operations performance requirements (5 ms)
    System Shutdown
      ✓ should orchestrate graceful system shutdown (6 ms)
      ✓ should orchestrate priority system shutdown (5 ms)
      ✓ should orchestrate emergency system shutdown (5 ms)
    Integration with Enhanced Components
      ✓ should integrate with all previous phase components (5 ms)
      ✓ should maintain backward compatibility with base MemorySafetyManager (9 ms)
      ✓ should handle enhanced metrics collection (5 ms)
    Performance and Compliance
      ✓ should meet all performance requirements (5 ms)
      ✓ should maintain Anti-Simplification Policy compliance (5 ms)
      ✓ should handle Jest fake timers compatibility (4 ms)
      ✓ should maintain memory usage within limits (4 ms)
    Factory Functions
      ✓ should create enhanced memory safety manager instances (4 ms)
      ✓ should handle singleton pattern correctly (4 ms)

  ● MemorySafetyManagerEnhanced › Component Discovery › should auto-integrate compatible components

    expect(received).toBe(expected) // Object.is equality

    Expected: true
    Received: false

      184 |       // ✅ First verify compatibility
      185 |       const compatibility = manager.validateComponentCompatibility(mockComponent);
    > 186 |       expect(compatibility.compatible).toBe(true);
          |                                        ^
      187 |
      188 |       const result = await manager.autoIntegrateComponent(mockComponent);
      189 |

      at Object.<anonymous> (shared/src/base/__tests__/MemorySafetyManagerEnhanced.test.ts:186:40)

Test Suites: 1 failed, 1 total
Tests:       1 failed, 19 passed, 20 total
Snapshots:   0 total
Time:        3.745 s
Ran all test suites matching /MemorySafetyManagerEnhanced.test.ts/i.
